describe('Test the login page', () => {
  // These will be functions returning Cypress chains
  let getEmailInput: () => Cypress.Chainable<JQuery<HTMLElement>>;
  let getPasswordInput: () => Cypress.Chainable<JQuery<HTMLElement>>;
  let getLoginButton: () => Cypress.Chainable<JQuery<HTMLElement>>;

  beforeEach(() => {
    cy.viewport(1086, 800);
    cy.visit('/login');

    // Define getter functions for the email and password inputs
    getEmailInput = (): Cypress.Chainable<JQuery<HTMLElement>> => {
      return cy.get('[data-test-id="email-input"]');
    };

    getPasswordInput = (): Cypress.Chainable<JQuery<HTMLElement>> => {
      return cy.get('[data-test-id="password-input"]');
    };
    getLoginButton = (): Cypress.Chainable<JQuery<HTMLElement>> => {
      return cy.get('[data-test-id="login-button"]');
    };
  });

  it('displays email and password inputs', () => {
    getEmailInput().should('be.visible');
    getPasswordInput().should('be.visible');
  });

  it('shows an error message when invalid credentials are entered', () => {
    cy.wait(1000);
    getEmailInput()
      .type('<EMAIL>')
      .should('have.value', '<EMAIL>');

    getPasswordInput().type('wrongpassword');

    getLoginButton().click();

    cy.get('.AlertMessage_root__ZDeqP').should('be.visible');
  });
});
