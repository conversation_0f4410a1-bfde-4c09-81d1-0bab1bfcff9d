import type { Parameters, Preview, ReactRenderer } from '@storybook/react';
import '../src/shared/theme/theme.css';
import '../src/shared/theme/cssReset.scss';
import '../src/shared/theme/globalCss.scss';
import '../src/shared/theme/tailwind.css';
import '../src/shared/theme/sb.styles.css';
import { DecoratorFunction, GlobalTypes } from 'storybook/internal/types';
import ThemeProvider from '../src/shared/uikit/ThemeProvider';
import { LanguageProvider } from '../src/shared/contexts/Language';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import * as React from 'react';
import { withConsole } from '@storybook/addon-console';
import { themes } from '@storybook/theming';
import { useDarkMode } from 'storybook-dark-mode';

const Providers = (Story) => {
  const isDark = useDarkMode();
  const [key, setKey] = React.useState(React.useId());

  React.useEffect(() => {
    setKey((prev) => prev + 1);
    const classList = Object.values(document.body.classList)
      .filter(
        (className) =>
          className !== 'sb-dark-theme' &&
          className !== 'sb-light-theme' &&
          className !== 'dark-theme' &&
          className !== 'light-theme'
      )
      .join(' ');

    document.body.className = `${classList} ${isDark ? 'sb-dark-theme dark-theme' : 'sb-light-theme light-theme'}`;
    const sbDuc = document.getElementById("storybook-docs");
    if(sbDuc) {
      sbDuc.className = `${classList} ${isDark ? 'sb-dark-theme dark-theme' : 'sb-light-theme light-theme'}`
    }
  }, [isDark]);

  return (
    <QueryClientProvider client={new QueryClient()}>
      <LanguageProvider lng="en">
        <ThemeProvider isDark={isDark} key={key}>
          <Story />
        </ThemeProvider>
      </LanguageProvider>
    </QueryClientProvider>
  );
};

const globalTypes: GlobalTypes = {};

const decorators:
  | DecoratorFunction<
      ReactRenderer,
      {
        [x: string]: any;
      }
    >
  | DecoratorFunction<
      ReactRenderer,
      {
        [x: string]: any;
      }
    >[] = [Providers, (storyFn, context) => withConsole()(storyFn)(context)];

const parameters: Parameters = {
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/i,
    },
  },
  darkMode: {
    dark: { ...themes.dark },
    light: { ...themes.normal },
  },
  layout: 'centered',
  nextjs: {
    appDirectory: true,
  },
};

const preview: Preview = {
  parameters,
  globalTypes,
  initialGlobals: {
    theme: 'light',
  },
  decorators,
};

export default preview;
