# Performance Conventions

## Table of Contents

- [Performance Conventions](#performance-conventions)
  - [Table of Contents](#table-of-contents)
  - [Performance Optimization](#performance-optimization)
    - [1. Code Splitting](#1-code-splitting)
    - [2. Image Optimization](#2-image-optimization)
    - [3. Virtualization](#3-virtualization)

## Performance Optimization

### 1. Code Splitting

Dynamic imports are a powerful optimization technique that offers several key benefits:

**Benefits of Dynamic Imports:**

- **Reduced Initial Bundle Size**: Components are loaded only when needed, decreasing the initial JavaScript payload
- **Fermium Load Time**: Users download smaller initial bundles, leading to faster page loads and better Core Web Vitals
- **Improved Build Performance**: Large components don't block the initial build process
- **Better Caching**: Changed components only require re-downloading their specific chunks
- **Progressive Enhancement**: Non-critical features can be loaded after the main content

**Performance Impact:**

- **Build Time**: Reduces initial build time since heavy components are processed separately
- **Render Time**: Initial page render is faster, but there may be a slight delay when the component is first loaded
- **Bundle Size**: Significantly reduces the initial bundle size, improving load times

```typescript
// ✅ Good - Dynamic imports for code splitting
const HeavyChart = React.lazy(() => import('./HeavyChart'));

const Dashboard: React.FC = () => {
  return (
    <div>
      <h1>Dashboard</h1>
      <React.Suspense fallback={<div>Loading chart...</div>}>
        <HeavyChart />
      </React.Suspense>
    </div>
  );
};

// ✅ Good - Next.js automatic route-based code splitting
// Next.js automatically handles code splitting for pages in the pages directory
// No manual routing setup required - use Next.js router instead

// ✅ Good - Next.js dynamic imports for client-side components
import dynamic from 'next/dynamic';

// Component that should only render on the client side
const HeavyChart = dynamic(() => import('./HeavyChart'), {
  loading: () => <div>Loading chart...</div>,
  ssr: false
});

// Component with custom loading and error handling
const InteractiveMap = dynamic(() => import('./InteractiveMap'), {
  loading: () => <div>Loading map...</div>,
  error: (error) => <div>Error loading map: {error.message}</div>,
  ssr: false
});

// ✅ Good - When to use ssr: true vs ssr: false
// Use ssr: false for:
// - Components that depend on browser APIs (window, document, navigator)
// - Interactive components that use mouse/keyboard events
// - Components that use browser-specific libraries
// - Analytics components that track user interactions
const MouseTracker = dynamic(() => import('./MouseTracker'), {
  ssr: false
});

// Use ssr: true (default) for:
// - SEO-critical content that should be crawled by search engines
// - Static content that doesn't depend on browser APIs
// - Components that need to render consistently on server and client
// - Accessibility-critical components
const SEOContent = dynamic(() => import('./SEOContent'), {
  ssr: true
});
```

### 2. Image Optimization

```typescript
// ✅ Good - Next.js Image component
import Image from 'next/image';

const UserAvatar: React.FC<UserAvatarProps> = ({ src, alt }) => {
  return (
    <div className="user-avatar">
      <Image
        src={src || '/default-avatar.png'}
        alt={alt}
        width={40}
        height={40}
        className="rounded-full"
        placeholder="blur"
        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
      />
    </div>
  );
};

// ✅ Good - Lazy loading images
const ImageGallery: React.FC<ImageGalleryProps> = ({ images }) => {
  return (
    <div className="image-gallery">
      {images.map((image, index) => (
        <Image
          key={image.id}
          src={image.url}
          alt={image.alt}
          width={300}
          height={200}
          loading={index < 6 ? 'eager' : 'lazy'} // First 6 images load eagerly
        />
      ))}
    </div>
  );
};
```

### 3. Virtualization

```typescript
// ✅ Good - Virtualized lists for large datasets using react-virtuoso
import { Virtuoso } from 'react-virtuoso';

const LargeUserList: React.FC<LargeUserListProps> = ({ users }) => {
  return (
    <Virtuoso
      style={{ height: '600px' }}
      data={users}
      itemContent={(user, index) => (
        <div className="user-row">
          <UserCard user={user} />
        </div>
      )}
    />
  );
};

// ✅ Good - Virtualized grid using react-virtuoso
import { VirtuosoGrid } from 'react-virtuoso';

const ProductGrid: React.FC<ProductGridProps> = ({ products }) => {
  return (
    <VirtuosoGrid
      style={{ height: '600px' }}
      data={products}
      itemContent={(product) => (
        <div className="product-cell">
          <ProductCard product={product} />
        </div>
      )}
      columns={3}
      listClassName="product-grid-list"
      itemClassName="product-grid-item"
    />
  );
};
```

_Last Updated: 4 August 2025_
_Version: 1.0_
