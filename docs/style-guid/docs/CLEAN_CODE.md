# Clean code Guidelines

## Table of Contents

- [Clean code Guidelines](#clean-code-guidelines)
  - [Table of Contents](#table-of-contents)
  - [1. Meaningful Names](#1-meaningful-names)
  - [2. Function Design](#2-function-design)
  - [3. <PERSON>rro<PERSON> Handling](#3-error-handling)
  - [4. Code Comments](#4-code-comments)

## 1. Meaningful Names

```typescript
// ✅ Good - Descriptive names
const calculateUserAge = (birthDate: Date): number => {
  const today = new Date();
  const birthYear = birthDate.getFullYear();
  const currentYear = today.getFullYear();
  return currentYear - birthYear;
};

// ❌ Avoid - Vague names
const calc = (d: Date): number => {
  const t = new Date();
  const b = d.getFullYear();
  const c = t.getFullYear();
  return c - b;
};
```

## 2. Function Design

```typescript
// ✅ Good - Single purpose, clear parameters
const validateUserInput = (
  email: string,
  password: string,
  age: number
): ValidationResult => {
  const errors: string[] = [];

  if (!isValidEmail(email)) {
    errors.push('Invalid email format');
  }

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters');
  }

  if (age < 18) {
    errors.push('User must be at least 18 years old');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// ❌ Avoid - Multiple responsibilities
const handleUser = (
  user: User,
  action: 'create' | 'update' | 'delete',
  sendEmail: boolean,
  logAction: boolean
) => {
  // Does too many things
};
```

## 3. Error Handling

```typescript
// ✅ Good - Proper error handling
const useUserData = (userId: string) => {
  const { data, error, isLoading } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => fetchUser(userId),
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error instanceof AuthError) return false;
      return failureCount < 3;
    },
  });

  if (error) {
    if (error instanceof NetworkError) {
      throw new Error('Network error. Please check your connection.');
    }
    if (error instanceof NotFoundError) {
      throw new Error('User not found');
    }
    throw error;
  }

  return { data, isLoading };
};

// ❌ Avoid - Generic error handling
const useUserData = (userId: string) => {
  const { data, error } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => fetchUser(userId),
  });

  if (error) {
    throw error; // No specific handling
  }

  return data;
};
```

## 4. Code Comments

```typescript
// ✅ Good - Comments that explain why, not what
/**
 * Use exponential backoff for retrying failed API requests
 * to avoid overwhelming the server with retry attempts.
 */
const useRetryableQuery = (queryKey: string, queryFn: () => Promise<any>) => {
  return useQuery({
    queryKey,
    queryFn,
    retry: (failureCount, error) => {
      const retryDelay = Math.pow(2, failureCount) * 1000; // Exponential backoff
      if (retryDelay > 30000) return false; // Max 30 seconds delay

      console.log(`Retrying in ${retryDelay}ms...`);
      return true;
    },
  });
};

// ❌ Avoid - Obvious comments
const add = (a: number, b: number): number => {
  const result = a + b; // Add a and b
  return result; // Return the result
};
```

---

_Last Updated: 4 August 2025_
_Version: 1.0_
