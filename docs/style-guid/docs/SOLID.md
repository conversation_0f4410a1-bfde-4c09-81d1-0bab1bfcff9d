# SOLID Conventions

## Table of Contents

- [SOLID Conventions](#solid-conventions)
  - [Table of Contents](#table-of-contents)
  - [Single Responsibility Principle (SRP)](#single-responsibility-principle-srp)
  - [Open/Closed Principle (OCP)](#openclosed-principle-ocp)
  - [Liskov Substitution Principle (LSP)](#liskov-substitution-principle-lsp)
  - [Interface Segregation Principle (ISP)](#interface-segregation-principle-isp)
  - [Dependency Inversion Principle (DIP)](#dependency-inversion-principle-dip)

## Single Responsibility Principle (SRP)

```typescript
// ✅ Good - Each component has one responsibility
const UserProfile: React.FC<UserProfileProps> = ({ user }) => {
  return (
    <div className="user-profile">
      <Avatar user={user} />
      <UserInfo user={user} />
      <UserActions user={user} />
    </div>
  );
};

// Separate components for different responsibilities
const Avatar: React.FC<AvatarProps> = ({ user }) => (
  <img src={user.avatar} alt={user.name} />
);

const UserInfo: React.FC<UserInfoProps> = ({ user }) => (
  <div>
    <h2>{user.name}</h2>
    <p>{user.email}</p>
  </div>
);

const UserActions: React.FC<UserActionsProps> = ({ user }) => (
  <div>
    <button>Edit Profile</button>
    <button>Message</button>
  </div>
);
```

## Open/Closed Principle (OCP)

```typescript
// ✅ Good - Open for extension, closed for modification
interface NotificationStrategy {
  send(message: string): void;
}

const EmailNotification: React.FC<NotificationProps> = ({ strategy }) => {
  const handleSend = () => {
    strategy.send('Hello via Email');
  };

  return <button onClick={handleSend}>Send Email</button>;
};

// New notification types can be added without modifying existing code
const SMSNotification: React.FC<NotificationProps> = ({ strategy }) => {
  const handleSend = () => {
    strategy.send('Hello via SMS');
  };

  return <button onClick={handleSend}>Send SMS</button>;
};

// Usage
const EmailNotificationComponent = (
  <EmailNotification strategy={new EmailStrategy()} />
);

const SMSNotificationComponent = (
  <SMSNotification strategy={new SMSStrategy()} />
);
```

## Liskov Substitution Principle (LSP)

```typescript
// ✅ Good - Subtypes can be substituted for their base types
interface BaseButtonProps {
  onClick: () => void;
  disabled?: boolean;
}

const BaseButton: React.FC<BaseButtonProps> = ({ onClick, disabled }) => (
  <button onClick={onClick} disabled={disabled}>
    Click me
  </button>
);

// Subtype maintains the same behavior
const PrimaryButton: React.FC<BaseButtonProps> = (props) => (
  <BaseButton {...props} className="primary-button" />
);

const SecondaryButton: React.FC<BaseButtonProps> = (props) => (
  <BaseButton {...props} className="secondary-button" />
);

// Usage - can be substituted
const ButtonGroup: React.FC = () => (
  <div>
    <PrimaryButton onClick={() => console.log('Primary')} />
    <SecondaryButton onClick={() => console.log('Secondary')} />
  </div>
);
```

## Interface Segregation Principle (ISP)

```typescript
// ✅ Good - Specific interfaces instead of one large interface
interface Clickable {
  onClick(): void;
}

interface Hoverable {
  onMouseEnter(): void;
  onMouseLeave(): void;
}

interface Focusable {
  onFocus(): void;
  onBlur(): void;
}

// Components implement only what they need
const Button: React.FC<Clickable & Hoverable> = ({
  onClick,
  onMouseEnter,
  onMouseLeave
}) => (
  <button
    onClick={onClick}
    onMouseEnter={onMouseEnter}
    onMouseLeave={onMouseLeave}
  >
    Button
  </button>
);

const Input: React.FC<Focusable & Clickable> = ({
  onFocus,
  onBlur,
  onClick
}) => (
  <input
    onFocus={onFocus}
    onBlur={onBlur}
    onClick={onClick}
  />
);
```

## Dependency Inversion Principle (DIP)

```typescript
// ❌ Bad - Direct dependency on concrete implementation
const handleClick = () => {
  console.log(`Button clicked: ${children}`); // Hard-coded dependency
};
```

```typescript
// ✅ Good - Depend on abstraction, not concrete implementation
// Component depends on Logger abstraction
const handleClick = () => {
  logger.log(`Button clicked: ${children}`);
};
```

---

_Last Updated: 4 August 2025_
_Version: 1.0_
