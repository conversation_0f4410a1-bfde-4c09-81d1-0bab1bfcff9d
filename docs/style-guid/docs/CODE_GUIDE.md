# Code Style & Conventions

## Table of Contents

- [Code Style \& Conventions](#code-style--conventions)
  - [Table of Contents](#table-of-contents)
  - [Airbnb React Convention](#airbnb-react-convention)
    - [General Rules](#general-rules)
    - [Component Structure](#component-structure)
    - [TypeScript Usage](#typescript-usage)
  - [Naming Conventions](#naming-conventions)
  - [File Organization](#file-organization)
  - [React Best Practices](#react-best-practices)
    - [Component Best Practices](#component-best-practices)
      - [1. Use Functional Components and Hooks](#1-use-functional-components-and-hooks)
      - [2. Proper Prop Types and Interfaces](#2-proper-prop-types-and-interfaces)
      - [3. Conditional Rendering](#3-conditional-rendering)
      - [4. List Rendering](#4-list-rendering)
    - [Hook Best Practices](#hook-best-practices)
      - [1. Custom Hooks](#1-custom-hooks)
      - [2. useEffect Best Practices](#2-useeffect-best-practices)
    - [Performance Optimization](#performance-optimization)
      - [1. React.memo for Components](#1-reactmemo-for-components)
      - [2. useMemo and useCallback](#2-usememo-and-usecallback)
  - [Design Patterns](#design-patterns)
    - [1. Component Composition Pattern](#1-component-composition-pattern)
    - [2. Render Props Pattern](#2-render-props-pattern)
    - [3. Higher-Order Component (HOC) Pattern](#3-higher-order-component-hoc-pattern)
    - [4. Custom Hook Pattern](#4-custom-hook-pattern)
    - [5. Compound Component Pattern](#5-compound-component-pattern)

---

## Airbnb React Convention

We strictly follow the [Airbnb React/JSX Style Guide](https://github.com/airbnb/javascript/tree/master/react) and [Airbnb Javascript Style Guid](https://github.com/airbnb/javascript).

### General Rules

- Use functional components with hooks
- Use TypeScript interfaces/types for all props
- Follow the ESLint configuration in `package.json`
- Use Prettier for code formatting

### Component Structure

```typescript
// ✅ Proper component structure
import React from 'react';
import { Button } from 'shared/uikit/Button';
import type { ComponentProps } from './types';

const Component: React.FC<ComponentProps> = ({
  title,
  isActive,
  onClick
}) => {
  const handleClick = () => {
    onClick?.();
  };

  return (
    <div className="component-wrapper">
      <h2>{title}</h2>
      <Button
        variant={isActive ? 'primary' : 'secondary'}
        onClick={handleClick}
      >
        Click me
      </Button>
    </div>
  );
};

Component.displayName = 'Component';

export default Component;
```

### TypeScript Usage

```typescript
// ✅ Proper TypeScript usage - following Interface Segregation Principle
interface UserData {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

interface UserActions {
  onEdit: (userId: string) => void;
  onDelete: (userId: string) => void;
}

interface UserCardProps extends UserData, UserActions {
  isLoading?: boolean;
}

const UserCard: React.FC<UserCardProps> = ({
  user,
  onEdit,
  onDelete,
  isLoading,
}) => {
  // Component implementation
};
```

**Note on Interface Segregation**: We follow the Interface Segregation Principle by breaking down large interfaces into smaller, focused interfaces. This prevents components from depending on methods they don't use and makes the code more maintainable and flexible. Each interface should have a single, well-defined purpose.

## Naming Conventions

- **Components**: PascalCase (`UserProfile`, `SearchBar`)
- **Files**: PascalCase for components, camelCase for utilities
- **Hooks**: camelCase starting with `use` (`useFetchData`, `useAuth`)
- **Constants**: UPPER_SNAKE_CASE (`API_BASE_URL`, `MAX_RETRY_ATTEMPTS`)
- **Variables/Functions**: camelCase (`userName`, `fetchUserData`)
- **Private methods**: camelCase with underscore prefix (`_internalMethod`)

## File Organization

- One component per file (except small related components)
- Group related utilities in the same file
- Use index files for clean imports
- Follow the established folder structure

---

## React Best Practices

### Component Best Practices

#### 1. Use Functional Components and Hooks

```typescript
// ✅ Good
const UserProfile: React.FC<UserProfileProps> = ({ userId }) => {
  const { data: user, isLoading, error } = useUser(userId);

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return <div>{user?.name}</div>;
};

// ❌ Avoid
class UserProfile extends React.Component<UserProfileProps> {
  componentDidMount() {
    // Class lifecycle methods
  }

  render() {
    // Class-based rendering
  }
}
```

#### 2. Proper Prop Types and Interfaces

```typescript
// ✅ Good - Comprehensive interface
interface LoginFormProps {
  onSubmit: (credentials: { email: string; password: string }) => Promise<void>;
  isLoading?: boolean;
  error?: string;
  className?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onSubmit,
  isLoading = false,
  error,
  className = '',
}) => {
  // Component implementation
};
```

#### 3. Conditional Rendering

```typescript
// ✅ Good - Early returns
const Component: React.FC<ComponentProps> = ({ data, isLoading }) => {
  if (isLoading) return <LoadingState />;
  if (!data) return <EmptyState />;

  return (
    <div>
      {data.map(item => (
        <Item key={item.id} item={item} />
      ))}
    </div>
  );
};

// ✅ Good - Ternary for simple conditions
const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => (
  <span className={status === 'active' ? 'text-green-600' : 'text-red-600'}>
    {status}
  </span>
);
```

#### 4. List Rendering

```tsx
// ✅ Good - Proper keys and optimization
const UserList: React.FC<UserListProps> = ({ users }) => {
  return (
    <ul>
      {users.map((user) => (
        <UserListItem
          key={user.id}
          user={user}
          onClick={() => handleUserClick(user.id)}
        />
      ))}
    </ul>
  );
};

// ❌ Avoid - Index as key
{
  users.map((user, index) => <UserListItem key={index} user={user} />);
}
```

### Hook Best Practices

#### 1. Custom Hooks

```typescript
// ✅ Good - Custom hook with clear purpose
const useAuth = () => {
  const { data: user, isLoading, error } = useQuery({
    queryKey: ['auth', 'user'],
    queryFn: fetchCurrentUser,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const login = useCallback(async (credentials: LoginCredentials) => {
    // Login logic
  }, []);

  const logout = useCallback(() => {
    // Logout logic
  }, []);

  return {
    user,
    isLoading,
    error,
    isAuthenticated: !!user,
    login,
    logout,
  };
};

// ✅ Usage in component
const UserProfile: React.FC = () => {
  const { user, isLoading, isAuthenticated } = useAuth();

  if (isLoading) return <LoadingSpinner />;
  if (!isAuthenticated) return <Redirect to="/login" />;

  return <div>Welcome, {user?.name}</div>;
};
```

#### 2. useEffect Best Practices

```typescript
// ✅ Good - Proper cleanup
const useWebSocket = (url: string) => {
  const [data, setData] = useState<string | null>(null);

  useEffect(() => {
    const ws = new WebSocket(url);

    ws.onmessage = (event) => {
      setData(event.data);
    };

    return () => {
      ws.close();
    };
  }, [url]);

  return data;
};

// ✅ Good - Dependency array optimization
const useUserData = (userId: string) => {
  const { data } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => fetchUser(userId),
    enabled: !!userId, // Only run when userId exists
  });

  return data;
};
```

### Performance Optimization

#### 1. React.memo for Components

```typescript
// ✅ Good - Memoizing expensive components
const ExpensiveList: React.FC<ExpensiveListProps> = ({ items }) => {
  // Expensive rendering logic
  return (
    <div>
      {items.map(item => (
        <ExpensiveItem key={item.id} item={item} />
      ))}
    </div>
  );
};

export default React.memo(ExpensiveList);

// ✅ Good - Memo with custom comparison
const UserCard: React.FC<UserCardProps> = React.memo(({
  user,
  onEdit,
  onDelete
}) => {
  return (
    <div className="user-card">
      <h3>{user.name}</h3>
      <button onClick={() => onEdit(user.id)}>Edit</button>
      <button onClick={() => onDelete(user.id)}>Delete</button>
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison logic
  return prevProps.user.id === nextProps.user.id &&
         prevProps.user.name === nextProps.user.name;
});
```

#### 2. useMemo and useCallback

```typescript
// ✅ Good - Memoizing expensive calculations
const UserList: React.FC<UserListProps> = ({ users, filter }) => {
  const filteredUsers = useMemo(() => {
    console.log('Filtering users...'); // This will only run when dependencies change
    return users.filter(user =>
      user.name.toLowerCase().includes(filter.toLowerCase())
    );
  }, [users, filter]);

  const handleUserClick = useCallback((userId: string) => {
    console.log('User clicked:', userId);
    navigateToUser(userId);
  }, []);

  return (
    <div>
      {filteredUsers.map(user => (
        <UserListItem
          key={user.id}
          user={user}
          onClick={handleUserClick}
        />
      ))}
    </div>
  );
};
```

---

## Design Patterns

### 1. Component Composition Pattern

```typescript
// ✅ Good - Using composition for flexibility
const Card: React.FC<CardProps> = ({ children, className = '' }) => (
  <div className={`card ${className}`}>
    {children}
  </div>
);

const CardHeader: React.FC<CardHeaderProps> = ({ children }) => (
  <div className="card-header">
    {children}
  </div>
);

const CardBody: React.FC<CardBodyProps> = ({ children }) => (
  <div className="card-body">
    {children}
  </div>
);

const CardFooter: React.FC<CardFooterProps> = ({ children }) => (
  <div className="card-footer">
    {children}
  </div>
);

// Usage
<Card>
  <CardHeader>
    <h2>User Profile</h2>
  </CardHeader>
  <CardBody>
    <p>User information goes here</p>
  </CardBody>
  <CardFooter>
    <button>Save Changes</button>
  </CardFooter>
</Card>
```

### 2. Render Props Pattern

```typescript
// ✅ Good - Render props for flexibility
const MouseTracker: React.FC<MouseTrackerProps> = ({ render }) => {
  const [position, setPosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setPosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return render(position);
};

// Usage
<MouseTracker render={({ x, y }) => (
  <div>
    Mouse position: {x}, {y}
  </div>
)} />
```

### 3. Higher-Order Component (HOC) Pattern

```typescript
// ✅ Good - HOC for cross-cutting concerns
const withAuth = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P & WithAuthProps> => {
  const AuthenticatedComponent: React.FC<P & WithAuthProps> = (props) => {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) return <LoadingSpinner />;
    if (!isAuthenticated) return <Redirect to="/login" />;

    return <Component {...props} />;
  };

  return AuthenticatedComponent;
};

// Usage
const ProtectedDashboard = withAuth(Dashboard);

// In a component
const ProtectedDashboardPage: React.FC = () => {
  return <ProtectedDashboard />;
};
```

### 4. Custom Hook Pattern

```typescript
// ✅ Good - Custom hook for complex logic
const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => clearTimeout(handler);
  }, [value, delay]);

  return debouncedValue;
};

// Usage
const SearchComponent: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  useEffect(() => {
    if (debouncedSearchTerm) {
      searchUsers(debouncedSearchTerm);
    }
  }, [debouncedSearchTerm]);

  return (
    <input
      type="text"
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      placeholder="Search users..."
    />
  );
};
```

### 5. Compound Component Pattern

```typescript
// ✅ Good - Compound components for complex UI
const Tabs: React.FC<TabsProps> & {
  Tab: React.FC<TabProps>;
  Panel: React.FC<PanelProps>;
} = ({ children, defaultValue }) => {
  const [activeTab, setActiveTab] = useState(defaultValue);

  return (
    <div className="tabs">
      <div className="tabs-header">
        {React.Children.map(children, (child, index) => {
          if (React.isValidElement(child) && child.type === Tabs.Tab) {
            return React.cloneElement(child, {
              isActive: child.props.value === activeTab,
              onClick: () => setActiveTab(child.props.value),
            });
          }
          return child;
        })}
      </div>
      <div className="tabs-content">
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child) && child.type === Tabs.Panel) {
            return child.props.value === activeTab ? child : null;
          }
          return null;
        })}
      </div>
    </div>
  );
};

Tabs.Tab: React.FC<TabProps> = ({ children, isActive, onClick }) => (
  <button
    className={`tab ${isActive ? 'active' : ''}`}
    onClick={onClick}
  >
    {children}
  </button>
);

Tabs.Panel: React.FC<PanelProps> = ({ children, value }) => (
  <div className="tab-panel" data-value={value}>
    {children}
  </div>
);

// Usage
<Tabs defaultValue="profile">
  <Tabs.Tab value="profile">Profile</Tabs.Tab>
  <Tabs.Tab value="settings">Settings</Tabs.Tab>

  <Tabs.Panel value="profile">
    <ProfileContent />
  </Tabs.Panel>
  <Tabs.Panel value="settings">
    <SettingsContent />
  </Tabs.Panel>
</Tabs>
```

---

_Last Updated: 4 August 2025_
_Version: 1.0_
