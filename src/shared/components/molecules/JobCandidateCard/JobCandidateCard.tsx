import { CandidateCardActions } from '@shared/components/molecules/CandidateCard';
import CandidateCard from '@shared/components/molecules/CandidateCard/CandidateCard';
import HorizontalTagList from '@shared/components/molecules/HorizontalTagList';
import { IsManualWrapper } from '@shared/components/molecules/IsManualWrapper';
import type { CandidateJobCardProps } from '@shared/types/jobsProps';
import type { PipelineInfo } from '@shared/types/pipelineProps';
import Button from '@shared/uikit/Button';
import CandidateScoreBar from '@shared/uikit/CandidateScoreBar/CandidateScoreBar';
import Flex from '@shared/uikit/Flex';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import { editCandidateAdditionalInfo } from '@shared/utils/api/candidates';
import { QueryKeys, routeNames } from '@shared/utils/constants';
import useHistory from '@shared/utils/hooks/useHistory';
import useTranslation from '@shared/utils/hooks/useTranslation';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo, type FC, type PropsWithChildren } from 'react';
import type {
  JobCandidateCardProps,
  OtherProps,
  JobCandidateCardInfoCardProps,
} from '@shared/components/molecules/JobCandidateCard/types';
import JobCandidateCardRightSection from './JobCandidateCardRightSection';

const JobCandidateCard: FC<
  PropsWithChildren<
    { data: CandidateJobCardProps } & OtherProps & JobCandidateCardProps
  >
> = ({ data, variant, onChangePipeline, onPrimaryButtonClick, ...props }) => {
  const { t } = useTranslation();
  const history = useHistory();

  const onClickAvatar = () =>
    history.push(routeNames.candidate.makeRoute(data?.id));

  const queryClient = useQueryClient();

  const refetch = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [QueryKeys.getCandidate, data.id],
      exact: false,
    });
  }, [data.id, queryClient]);

  const isLoboxUser = !!data?.candidate?.profile?.username;

  const items = useMemo<JobCandidateCardInfoCardProps[]>(
    () => [
      {
        key: 'years_of_experience',
        title: t('years_of_experience'),
        value: data?.yearsOfExperience,
        icon: 'briefcase-blank-light',
      },
      {
        key: 'latest_degree',
        title: t('latest_degree'),
        value: t(data?.lastDegree),
        icon: 'school',
      },
      {
        key: 'related_skills_to_job',
        title: t('related_skills_to_job'),
        value: data?.relatedSkillsCount,
        icon: 'medal',
      },
      {
        key: 'matching_expectations',
        title: t('matching_expectations'),
        value: data?.matchingExpectationsCount,
        icon: 'expectation',
      },
      {
        key: 'related_languages_to_job',
        title: t('related_languages_to_job'),
        value: data?.relatedLanguagesCount,
        icon: 'language',
      },
    ],
    [data, t]
  );

  return (
    <Flex flexDir="row">
      <CandidateCard
        avatar={data?.image}
        firstText={data?.fullName}
        secondText={data?.username}
        thirdText={data?.occupation}
        fourthText={cleanRepeatedWords(data?.location || '')}
        treeDotMenu={
          <CandidateCardActions
            candidate={{
              ...data?.candidate,
              profile: {
                ...data?.candidate?.profile,
                occupation: { label: data?.candidate?.profile?.occupationName },
                location: { title: data?.job?.location?.title },
                usernameAtSign: `@${data?.candidate?.profile?.username}`,
              },
            }}
          />
        }
        FirstTextWrapper={!isLoboxUser ? IsManualWrapper : undefined}
        classNames={{ root: '!rounded-r-none gap-20 flex-1' }}
        avatarProps={{
          onClick: onClickAvatar,
        }}
      >
        <CandidateScoreBar score={data?.avgScore || 0} />
        <HorizontalTagList
          className="w-[500px]"
          areaClassName="!px-0"
          tags={data?.candidate?.tags?.filter(Boolean)}
          title={t('candidate_tags')}
          editable
          onSuccess={refetch}
          apiFunc={(body) =>
            editCandidateAdditionalInfo({
              candidateId: data?.candidate.id,
              body,
            })
          }
        />
        <Flex className="!flex-row gap-12">
          {data?.username ? (
            <JobCandidateCardStageButton
              pipelines={data?.job?.pipelines}
              onChangePipeline={onChangePipeline}
              stageTitle={data?.pipeline?.title || ''}
            />
          ) : (
            <Button
              disabled
              className="flex-1"
              schema="semi-transparent"
              leftIcon="envelope"
              leftType="far"
              label={t('message')}
              fullWidth
            />
          )}
          <Button
            className="w-full"
            label={t('manage')}
            leftIcon="user-cog"
            fullWidth
            onClick={(e) => {
              e?.stopPropagation();
              onPrimaryButtonClick();
            }}
          />
        </Flex>
      </CandidateCard>
      <JobCandidateCardRightSection items={items} />
    </Flex>
  );
};

function JobCandidateCardStageButton({
  pipelines,
  stageTitle,
  onChangePipeline,
}: {
  pipelines?: PipelineInfo[];
  stageTitle: string;
  onChangePipeline: (pipeline: string) => void;
}) {
  const { t } = useTranslation();

  return (
    <PopperMenu
      placement="bottom"
      popperWidth={(width) => width}
      buttonComponent={() => (
        <Button
          schema="semi-transparent"
          fullWidth
          label={t(stageTitle)}
          labelProps={{ fontWeight: 700, size: 15 }}
          rightIcon="chevron-down"
          rightSize={12}
        />
      )}
    >
      {pipelines?.map((pipeline) => (
        <PopperItem
          key={pipeline.id}
          label={t(pipeline.title)}
          onClick={() => onChangePipeline(pipeline.id)}
        />
      ))}
    </PopperMenu>
  );
}

export default JobCandidateCard;
