import useGetPagePlanInfo from '@shared/hooks/api-hook/useGetPagePlanInfo';
import Button from '@shared/uikit/Button';
import useTranslation from '@shared/utils/hooks/useTranslation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import type { FeatureName } from '@shared/types/planRestriction';
import type { ButtonProps } from '@shared/uikit/Button';

interface FeaturePriceButtonProps extends ButtonProps {
  featureName: FeatureName;
}

export const FeaturePriceButton = ({
  featureName,
  label,
  ...rest
}: FeaturePriceButtonProps) => {
  const { t } = useTranslation();
  const { data: planInfo, isLoading } = useGetPagePlanInfo();

  const price =
    isLoading || !planInfo?.enrolledPlanInfoList
      ? undefined
      : planInfo.paidPerCallFeatureInfoList.find(
          (i) => i.featureName === featureName
        )?.price;

  const pricedLabel = price
    ? translateReplacer(t(`LABEL_${featureName}_PRICE`), [`${price}`])
    : label;

  return <Button label={pricedLabel} {...rest} />;
};
