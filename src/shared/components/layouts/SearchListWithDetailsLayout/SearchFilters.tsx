import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import SearchFiltersHeader from '../../Organism/SearchFiltersHeader';
import SearchFilterIcon from '../../molecules/SearchFilterIcon';
import classes from './index.module.scss';

export interface SearchFiltersProps {
  isLoading?: boolean;
  groups?: any;
  isTotalyEmpty?: boolean;
  classNames?: {
    root?: string;
    headerRoot?: string;
    filtersWrapper?: string;
    rightWrapper?: string;
  };
  hasBackBtn?: boolean;
  onBackHandler?: () => void;
  hasFilterIcon?: boolean;
  headerComponents?: React.ReactNode;
  isFullWidth?: boolean;
  filterTopComponent?: React.ReactElement;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  isLoading,
  groups,
  isTotalyEmpty,
  classNames,
  hasBackBtn,
  onBackHandler,
  hasFilterIcon,
  headerComponents,
  isFullWidth,
  filterTopComponent,
}) => (
  <Flex>
    {filterTopComponent}
    <Flex className={cnj(classes.linksRootShrink)}>
      <Flex
        className={cnj(
          classes.searchFilterContent,
          !isFullWidth && classes.filterMaxWidth,
          classNames?.headerRoot
        )}
      >
        {hasBackBtn && (
          <IconButton
            onClick={onBackHandler}
            name="chevron-left"
            type="far"
            className={classes.backBtn}
          />
        )}
        <div
          className={classes.filtersScroller}
          tabIndex={0}
          aria-label="Search filters scroller"
          role="region"
        >
          <SearchFiltersHeader
            isLoading={Boolean(isLoading)}
            groups={groups}
            className={classNames?.filtersWrapper}
          />
        </div>
        <Flex className={cnj(classes.rightWrapper, classNames?.rightWrapper)}>
          {hasFilterIcon && <SearchFilterIcon disabled={isLoading} />}
          {headerComponents}
        </Flex>
      </Flex>
    </Flex>
  </Flex>
);
export default SearchFilters;
