@import '/src/shared/theme/theme.scss';

@layer layout {
  .contentRoot {
    background: colors(background2);
    flex-wrap: unset;
    flex: 1;
    width: 100%;
    padding: 0;
  }

  .jobsListWithDetails {
    justify-content: center;
  }

  .details {
    display: none;
    height: 100%;
    overflow-y: auto !important;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .mobileHeader {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    z-index: 503;
    background: colors(background);
  }

  .linksRootShrink {
    background: colors(background);
    min-width: 0;
  }

  .searchFilterContent {
    flex-direction: row;
    align-items: center;
    padding: variables(gutter) * 0.5 0;
    min-width: 0;
  }

  .backBtn {
    display: none;
  }

  .rightWrapper {
    flex: 0 0 auto;
    flex-shrink: 0;
  }

  .filtersScroller {
    display: block !important;
    flex: 1 1 auto;
    min-width: 0;
    max-width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    touch-action: pan-x;
    overscroll-behavior-x: contain;
    scrollbar-gutter: stable both-edges;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
      background: transparent;
    }
  }

  .backBtn {
    flex-shrink: 0;
  }

  .mobileHeaderBackButton {
    display: none;
  }

  .searchIon {
    margin-left: auto;
  }
  .root {
    display: none;
  }

  @media (min-width: breakpoints(tablet)) {
    .jobsListWithDetails {
      flex-direction: row;
      height: 100%;
    }

    .list {
      flex: 1;
      overflow-y: auto;

      &::-webkit-scrollbar {
        display: none;
      }
    }
    .listFullWidth {
      flex: unset;
      width: 470px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        display: none;
      }
    }
    .details {
      padding: variables(largeGutter) 0 0 variables(largeGutter);
      display: flex;
      flex: 1.3;
      overflow: hidden;
      & > div {
        flex: 1;
      }
    }
    .detailsFullWidth {
      flex: 1;
      & > div {
        flex: unset;
      }
    }

    .linksRootShrink {
      flex-direction: row;
      padding: 0 variables(largeGutter);
    }

    .mobileHeader {
      display: none;
    }

    .searchFilterContent {
      padding: 0;
      height: variables(headerMobileHeight);
      margin: 0 auto;
      flex: 1;
    }
    .filterMaxWidth {
      max-width: min(variables(wideContentMaxWidth), 100%);
    }

    .contentRoot {
      padding: 0 variables(largeGutter);
      &:has(div[data-full-width]) {
        max-width: unset;
      }
    }

    .backBtn {
      display: flex;
      width: 30px;
      margin-right: variables(gutter) * 0.5;
    }

    .rightWrapper {
      display: flex;
      flex-direction: row;
      padding-left: variables(gutter);
    }

    .maxWidth {
      max-width: calc(
        variables(wideContentMaxWidth) + 2 * variables(largeGutter)
      );
      place-self: center;
      padding-left: variables(largeGutter) !important;
      padding-right: variables(largeGutter) !important;
    }
    .root {
      display: flex;
    }
  }
}
