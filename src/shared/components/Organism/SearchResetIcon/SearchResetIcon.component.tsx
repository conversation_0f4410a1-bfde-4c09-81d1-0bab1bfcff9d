import React from 'react';
import Button from 'shared/uikit/Button';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useJobsState } from 'shared/providers/JobsPorvider/jobs.provider';
import useSearchFilters from '@shared/hooks/searchFilters/useSearchFilters';

export interface SearchResetIconProps {
  className?: string;
  onClick?: VoidFunction;
}

const SearchResetIcon: React.FC<SearchResetIconProps> = ({
  className,
  onClick: parentOnClick,
}) => {
  const { resetFilters } = useSearchFilters();
  const { t } = useTranslation();
  const filtersWrapperRef = useJobsState('filtersWrapperRef');

  const onClick = () => {
    resetFilters();
    if (!filtersWrapperRef?.current) return;
    filtersWrapperRef.current.scrollTo(0, 0);
  };

  return (
    <Button
      className={className}
      schema="ghost"
      labelProps={{ color: 'primaryText' }}
      label={t('reset')}
      onClick={parentOnClick ?? onClick}
    />
  );
};

export default SearchResetIcon;
