'use client';

import dynamic from 'next/dynamic';
import useGetPagePlanInfo from '@shared/hooks/api-hook/useGetPagePlanInfo';
import usePlanRestriction from '@shared/hooks/usePlanRestriction';
import type { FeatureName } from '@shared/types/planRestriction';

const PlanRestrictionCardComponent = dynamic(
  () => import('./PlanRestrictionCard.card'),
  {
    ssr: false,
  }
);

export default function PlanRestrictionCard({
  featuresName,
  className,
}: {
  featuresName: FeatureName[];
  className?: string;
}) {
  const { data: planInfo } = useGetPagePlanInfo();
  const { events } = usePlanRestriction();
  const feature =
    events.find((i) => i.exceededFeature === featuresName[0]) || events[0];
  console.log({ planInfo });

  if (!feature) {
    return null;
  }

  return (
    <PlanRestrictionCardComponent className={className} feature={feature} />
  );
}
