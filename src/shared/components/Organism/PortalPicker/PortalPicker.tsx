import { useRouter } from 'next/navigation';
import React, { useRef } from 'react';
import Button from '@shared/uikit/Button';
import { routeNames } from '@shared/utils/constants';
import collectionToObjectByKey from '@shared/utils/toolkit/collectionToObjectByKey';
import Flex from 'shared/uikit/Flex';
import PopperMenu from 'shared/uikit/PopperMenu';
import useMedia from 'shared/uikit/utils/useMedia';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './PortalPicker.module.scss';

const options = [
  {
    value: 'projects',
    label: 'projects',
    icon: 'projects-light',
    routName: routeNames.searchRecruiterProjects,
  },
  {
    value: 'candidates',
    label: 'candidates',
    icon: 'candidates-light',
    routName: routeNames.searchCandidates,
  },
  {
    value: 'recruiterJobs',
    label: 'jobs',
    icon: 'briefcase-blank',
    routName: routeNames.searchRecruiterJobs,
  },
  {
    value: 'companies',
    label: 'companies',
    icon: 'companies',
    routName: routeNames.companies.search,
  },
];

interface PortalPickerProps {
  value: any;
  disabled?: boolean;
}

const PortalPicker: React.FC<PortalPickerProps> = ({
  value = 'projects',
  disabled,
}) => {
  const popperRef = useRef<any>(null);
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const selected = collectionToObjectByKey(options)?.[value] || {};
  const router = useRouter();

  const onClickOutside = () => {
    if (!isMoreThanTablet) return;
    popperRef.current?.close?.();
  };
  const onSelect = () => {
    popperRef.current?.close?.();
  };

  return (
    <PopperMenu
      bottomSheetClassName={classes.bottomSheetClassName}
      ref={popperRef}
      placement="bottom-start"
      menuClassName={classes.menuClassName}
      disableCloseOnClickInSide
      disableCloseOnClickOutSide={!isMoreThanTablet}
      onCloseOutside={onClickOutside}
      disablePortal={disabled}
      buttonComponent={(visible: boolean) => (
        <Button
          disabled={disabled}
          className="gap-8 !py-8 !px-12 !h-[40px] !justify-between !rounded-[0px] !w-[164px]"
          label={t(selected.label)}
          leftIcon={selected?.icon}
          leftType="far"
          rightIcon={visible ? 'chevron-up' : 'chevron-down'}
          schema="semi-transparent3"
        />
      )}
    >
      <Flex className={classes.listWrapper}>
        {options.map(({ value: v, icon, label, routName }) => {
          const isActive = v === value;

          return (
            <Button
              to={routName}
              onClick={onSelect}
              key={icon}
              className="gap-8 !p-8 !h-[40px] !justify-start !min-w-[144px]"
              label={t(label)}
              leftIcon={icon}
              leftType="far"
              rightIcon={isActive ? 'check' : undefined}
              schema={isActive ? 'primary-blue' : 'ghost'}
            />
          );
        })}
      </Flex>
    </PopperMenu>
  );
};

export default PortalPicker;
