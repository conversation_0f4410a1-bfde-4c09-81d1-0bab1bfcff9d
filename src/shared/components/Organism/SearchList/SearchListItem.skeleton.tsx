import React from 'react';
import { CandidateCardSkeleton } from '@shared/components/molecules/CandidateCard';
import BaseProjectCardSkeleton from '@shared/components/molecules/ProjectCard/BaseProjectCardSkeleton';
import TodoItemSkeleton from '@shared/components/molecules/TodoItem/TodoItemSkeleton';
import { BusinessJobCardSkeleton } from 'shared/components/molecules/BusinessJobCard';
import Flex from 'shared/uikit/Flex/Flex.component';
import Skeleton from 'shared/uikit/Skeleton/Skeleton';
import cnj from 'shared/uikit/utils/cnj';
import classes from './SearchListItem.module.scss';
import type { ISearchEntity } from '@shared/hooks/searchFilters/useSearchResultWithFilters';

export interface SearchListItemSkeletonProps {
  type: ISearchEntity;
}

const SearchListItemSkeleton: React.FC<SearchListItemSkeletonProps> = ({
  type,
}) => {
  const imageList = {
    jobs: classes.avatar_jobs,
    posts: classes.avatar_posts,
    pages: classes.avatar_pages,
    people: classes.avatar_people,
  };

  if (type === 'recruiterProjects')
    return (
      <BaseProjectCardSkeleton className={classes.item} showAssignees={false} />
    );

  if (type === 'recruiterJobsInner')
    return <BusinessJobCardSkeleton className={classes.item} showTags />;

  if (type === 'recruiterJobs')
    return <BusinessJobCardSkeleton className={classes.item} />;

  if (type === 'todos')
    return <TodoItemSkeleton classNames={{ root: classes.item }} />;

  if (type === 'candidates')
    return <CandidateCardSkeleton className={classes.item} showBadges />;

  return (
    <Flex className={cnj(classes.root, type === 'jobs' && classes.root_jobs)}>
      <Skeleton className={cnj(classes.avatar, imageList[type])} />
      <Flex
        className={cnj(
          classes.wrapper,
          type === 'posts' && classes.postWrapper
        )}
      >
        {type === 'posts' ? (
          <>
            <Flex className={classes.postDetail}>
              <Skeleton className={classes.postDetail_a} />
              <Skeleton className={classes.postDetail_t} />
            </Flex>
            <Skeleton className={classes.postTime} />
          </>
        ) : (
          <Skeleton className={classes.details} />
        )}
      </Flex>
      {type === 'jobs' && (
        <Flex className={classes.jobsSpecsContainer}>
          <Flex className={classes.jobsSpecs}>
            <Skeleton className={classes.jobsSpecs_f1} />
            <Skeleton className={classes.jobsSpecs_f2} />
            <Skeleton className={classes.jobsSpecs_f3} />
            <Skeleton className={classes.jobsSpecs_f4} />
          </Flex>
          <Skeleton className={classes.jobsSpecs_f5} />
        </Flex>
      )}
    </Flex>
  );
};

export default SearchListItemSkeleton;
