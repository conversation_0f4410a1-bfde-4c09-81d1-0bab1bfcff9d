import React from 'react';
import useSearchFilters from '@shared/hooks/searchFilters/useSearchFilters';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import useMedia from 'shared/uikit/utils/useMedia';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SearchFiltersForm from './SearchFiltersForm';
import classes from './SearchFiltersModal.component.module.scss';
import type {
  SearchFiltersQueryParamsType,
  SearchFiltersValueType,
} from 'shared/types/search';

export interface SearchFiltersModalProps {
  onClose: () => void;
  groups?: any;
  justUseCustom?: boolean;
  isFullWidth?: boolean;
}

const SearchFiltersModal: React.FC<SearchFiltersModalProps> = ({
  onClose,
  groups,
  justUseCustom = false,
  isFullWidth,
}) => {
  const { isTabletAndLess } = useMedia();
  const { t } = useTranslation();
  const { resetFilters, addToDynamicFiltersAndSetFilter } = useSearchFilters();

  const onCloseHandler = () => {
    onClose();
  };

  const onClickOutside = () => {
    if (isTabletAndLess) return;
    onCloseHandler();
  };

  const onSuccessHandler = async (
    variables: Record<SearchFiltersQueryParamsType, SearchFiltersValueType>
  ) => {
    const values = Object.keys(variables)?.reduce((acc, key) => {
      const value = variables?.[key];
      if (!hasValue(value)) return acc;
      acc[key] = value;

      return acc;
    }, {});
    onCloseHandler();
    if (!justUseCustom) {
      addToDynamicFiltersAndSetFilter({
        ...values,
        [searchFilterQueryParams.searchGroupType]: searchGroupTypes.ALL,
      });
    }
  };

  const resetFormHandler = () => {
    resetFilters();
    onCloseHandler();
  };

  return (
    <FixedRightSideModalDialog
      onBack={onCloseHandler}
      onClose={onCloseHandler}
      onClickOutside={onClickOutside}
      visibleBackdrop
      contentClassName="!max-w-full"
      {...(isFullWidth && {
        wide: true,
        fullBackdrop: true,
        doubleColumn: true,
      })}
    >
      <ModalHeaderSimple
        backButtonProps={{ onClick: onCloseHandler }}
        closeButtonProps={{ onClick: onCloseHandler }}
        className={classes.header}
        visibleHeaderDivider={isTabletAndLess}
        title={t('all_filters')}
      />
      <SearchFiltersForm
        groups={groups}
        local
        onSuccess={onSuccessHandler}
        enableReinitialize
        onReset={resetFormHandler}
        isFullWidth={isFullWidth}
      />
    </FixedRightSideModalDialog>
  );
};

export default SearchFiltersModal;

function hasValue(value: string | any[]) {
  return Array.isArray(value) ? Object.keys(value)?.length > 0 : !!value;
}
