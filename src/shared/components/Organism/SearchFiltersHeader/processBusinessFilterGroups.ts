import isEmptyObjectValues from 'shared/utils/toolkit/isEmptyObjectValues';
import { hasValue } from './hasValue';

export function processBusinessFilterGroups(
  groups: Array<Record<string, any>>,
  minItems: number = 6,
  maxItems: number = 12
) {
  const processedGroups = groups.map((curr, currentIndex) => {
    const isEmpty = isEmptyObjectValues({ val: curr.getValue() });
    const hasOptions = Array.isArray(curr.options) && curr.options.length > 0;
    const hasActualValue = hasValue(curr.getValue(), curr?.isDefaultValue);
    const order = curr.order || currentIndex + 7;

    return {
      ...curr,
      isEmpty,
      hasOptions,
      hasActualValue,
      order,
    };
  });

  // Filter based on inclusion rules
  const filteredGroups = processedGroups.filter((group) => {
    // If hiddenInHeader is true but has actual value, still include it
    if (group.hiddenInHeader) {
      return false;
    }

    // Always include if alwaysShowInHeader is true
    if (group.alwaysShowInHeader) {
      return true;
    }

    // If alwaysShowInHeader is false or not set, only ignore if:
    // - isEmpty AND options is empty array
    if (
      group.isEmpty &&
      Array.isArray(group.options) &&
      group.options.length === 0
    ) {
      return false;
    }

    return true;
  });

  // Sort based on priority first
  const sortedGroups = filteredGroups.sort((a, b) => {
    // Priority 1: alwaysShowInHeader
    if (a.alwaysShowInHeader && !b.alwaysShowInHeader) return -1;
    if (!a.alwaysShowInHeader && b.alwaysShowInHeader) return 1;

    // If both have alwaysShowInHeader, sort by order
    if (a.alwaysShowInHeader && b.alwaysShowInHeader) {
      return a.order - b.order;
    }

    // Priority 2: Items with actual values
    if (a.hasActualValue && !b.hasActualValue) return -1;
    if (!a.hasActualValue && b.hasActualValue) return 1;

    // If both have values, sort by order
    if (a.hasActualValue && b.hasActualValue) {
      return a.order - b.order;
    }

    // Priority 3: Items with options but no values
    if (a.hasOptions && !b.hasOptions) return -1;
    if (!a.hasOptions && b.hasOptions) return 1;

    // Final sort by order
    return a.order - b.order;
  });

  // Ensure minimum items
  if (sortedGroups.length < minItems) {
    const excludedGroups = processedGroups.filter(
      (group) => !sortedGroups.includes(group)
    );
    const additionalNeeded = minItems - sortedGroups.length;
    const additionalGroups = excludedGroups.slice(0, additionalNeeded);

    return [...sortedGroups, ...additionalGroups];
  }

  // Ensure maximum items
  if (sortedGroups.length > maxItems) {
    return sortedGroups.slice(0, maxItems);
  }

  return sortedGroups;
}
