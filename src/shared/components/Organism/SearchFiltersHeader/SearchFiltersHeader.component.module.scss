@import '/src/shared/theme/theme.scss';

@layer organism {
  .searchFiltersHeader {
    display: inline-flex; /* shrink-to-content so parent scroller can scroll */
    flex-wrap: nowrap;
    align-items: center;
    gap: 0; /* spacing comes from item margins */
    width: max-content; /* allow content to define width for scrolling */
    > * {
      flex: 0 0 auto; /* each item keeps its intrinsic width */
    }
  }
  .jobFilter {
    display: inline-flex;
    flex-shrink: 0;
    margin-right: variables(gutter) * 0.5;
    width: auto !important;
    max-width: none !important;
  }
  .searchResetMobile {
    display: flex;
    margin-right: variables(gutter);
  }

  .skeleton {
    display: inline-block;
    flex-shrink: 0;
    height: 32px;
    min-height: 32px;
    width: 107px;
    border-radius: 999px;
    margin-right: variables(gutter) * 0.5;
    &:first-child {
      border-radius: 4px;
    }
  }

  @media (min-width: breakpoints(tablet)) {
    .jobFilter {
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
