import { useSearchParams } from 'next/navigation';
import React, { useMemo } from 'react';
import useSearchFilters from '@shared/hooks/searchFilters/useSearchFilters';
import cnj from '@shared/uikit/utils/cnj';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import Skeleton from 'shared/uikit/Skeleton';
import collectionToObjectByKey from 'shared/utils/toolkit/collectionToObjectByKey';
import SearchResetIcon from '../SearchResetIcon';
import { hasValue } from './hasValue';
import { processBusinessFilterGroups } from './processBusinessFilterGroups';
import { processUserFilterGroups } from './processUserFilterGroups';
import classes from './SearchFiltersHeader.component.module.scss';
import SearchFiltersHeaderItem from './SearchFiltersHeader.item';
import type { SearchResetIconProps } from '../SearchResetIcon/SearchResetIcon.component';

export interface SearchFiltersHeaderProps {
  className?: string;
  groups: Array<Record<string, any>>;
  isLoading: boolean;
  resetSearchProps?: SearchResetIconProps;
  disabled?: boolean;
}

const skeletons = Array(8)?.fill(null);

const SearchFiltersHeader: React.FC<SearchFiltersHeaderProps> = ({
  groups,
  isLoading,
  resetSearchProps,
  disabled,
  className,
}) => {
  const searchParams = useSearchParams();
  const searchGroupType =
    searchParams.get(searchFilterQueryParams.searchGroupType) ||
    searchGroupTypes.ALL;
  const isAllSearchGroupType =
    !isBusinessApp && searchGroupType === searchGroupTypes.ALL;
  const { addToDynamicFiltersAndSetFilter, getLabelOrValue, searchFilters } =
    useSearchFilters();

  const filters = useMemo(
    () =>
      isLoading
        ? []
        : isBusinessApp
          ? processBusinessFilterGroups(groups)
          : processUserFilterGroups(groups),
    [groups, isLoading]
  );

  const visibleReset = useMemo(
    () =>
      filters
        ?.filter((item) => item?.name !== searchFilterQueryParams.searchEntity)
        ?.some((filter) =>
          hasValue(filter?.getValue?.(), filter?.isDefaultValue)
        ),
    [filters]
  );

  const skeletonFillers = skeletons.slice(0, Math.max(6 - filters?.length, 0));

  return (
    <div className={cnj(classes.searchFiltersHeader, className)}>
      {filters.map(
        ({
          name,
          cp,
          label,
          options,
          getValue,
          defaultValue,
          isDefaultValue,
          ...rest
        }) => {
          const value =
            cp === 'checkBoxGroup'
              ? options?.filter((item) => getValue()?.includes(item.value))
              : getValue();
          const getLabel = () => {
            if (cp === 'radioGroup' && value && !isDefaultValue) {
              return collectionToObjectByKey(options)?.[value]?.label;
            }

            return label;
          };

          return (
            <SearchFiltersHeaderItem
              className={classes.jobFilter}
              key={name}
              type={cp === 'dropdownSelect' ? 'radioGroup' : cp}
              value={value}
              name={name}
              options={options}
              label={isAllSearchGroupType ? getLabel() : label}
              defaultValue={defaultValue}
              isDefaultValue={isDefaultValue}
              disabled={disabled && !visibleReset && cp !== 'list'}
              addToDynamicFiltersAndSetFilter={addToDynamicFiltersAndSetFilter}
              getLabelOrValue={getLabelOrValue}
              searchFilters={searchFilters}
              {...rest}
            />
          );
        }
      )}
      {isLoading && (
        <>
          {skeletonFillers.map((_, i) => (
            <Skeleton key={`s___${i}`} className={classes.skeleton} />
          ))}
        </>
      )}
      {visibleReset && (
        <SearchResetIcon
          {...resetSearchProps}
          className={classes.searchResetMobile}
        />
      )}
    </div>
  );
};

export default SearchFiltersHeader;
