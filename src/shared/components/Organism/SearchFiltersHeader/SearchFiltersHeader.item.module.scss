@import '/src/shared/theme/theme.scss';

@layer organism {
  .bottomSheetClassName {
    padding-bottom: 0 !important;
  }
  .buttonRoot {
    padding: 4px 8px 4px 4px;
    flex-direction: row;
    align-items: center;
    border-radius: 50px;
    background-color: colors(brand_10);
    max-height: 32px;
    min-height: 32px;
    height: 32px;
    border: 1px solid colors(transparent);
    transition: smooth;

    &:hover {
      background-color: colors(brand_20);
    }
  }

  .listButtonRoot {
    border-radius: 4px;
    background-color: colors(darkBrand);

    &:hover {
      background-color: colors(darkBrandHover);
    }
  }

  .activeButtonRoot {
    background-color: colors(brand);

    &:hover {
      background-color: colors(brand);
    }
  }

  .visibleButtonRoot {
    border: 1px solid colors(brand);
  }
  .disableButtonRoot {
    opacity: 0.3;
  }

  .chevronIcon {
    margin-left: variables(gutter) * 0.5;
    background-color: colors(transparent);

    &:hover {
      background-color: colors(transparent);
    }

    &:active {
      background-color: colors(transparent);
    }
  }

  .label {
    padding-left: variables(gutter) * 0.5;
    color: colors(brand);
    overflow: hidden;
  }
  .listItemLabel {
    margin-left: 0;
  }

  .listWrapper {
    padding: 4px;
    padding-bottom: calc(
      #{variables(safeAreaInsetBottom)} + variables(gutter) / 4
    );
  }

  .salaryPickerWrapper {
    padding: variables(gutter) * 0.5 0;
  }

  .listLabel {
    color: colors(white);
  }

  .activeLabel {
    color: colors(white);
  }

  .listItem {
    justify-content: flex-start;
    background-color: colors(gray_5);
    margin-top: variables(gutter) * 0.5;
    gap: 8px;

    &_active {
      background-color: colors(darkBrand);
    }

    &:first-child {
      margin-top: 0;
    }
  }

  .labelsContainer {
    margin-left: variables(gutter);
  }

  .listItemIconButton {
    background-color: colors(hover75_hover2);

    &_active {
      background-color: colors(brand_10);
    }
  }

  .leftIcon {
    display: none;
    background-color: colors(darkSecondary10_white10);

    &:hover {
      background-color: colors(darkSecondary10_white10);
    }

    &:active {
      background-color: colors(darkSecondary10_white10);
    }
  }

  .divider {
    min-width: variables(gutter) * 0.5;
  }

  .menuClassName {
    padding: 0;
  }

  .childrenWrapper {
    max-height: 60vh;
    overflow: auto;
    margin: variables(gutter) * 0.5;
  }

  .buttonsWrapper {
    padding: variables(gutter);
    padding-bottom: calc(#{variables(safeAreaInsetBottom)} + variables(gutter));
    border-top: 1px solid colors(techGray_10);
    flex-direction: row;
  }

  .badge {
    width: 24px;
    height: 24px;
    min-height: 24px;
    min-width: 24px;
    max-height: 24px;
    max-width: 24px;
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    background-color: colors(white);
  }

  .applyAt {
    padding-right: variables(xLargeGutter) * 0.5;
  }

  .checkBoxGroup {
    padding: 12px 8px;
  }

  .itemWrapper {
    margin-bottom: 0;
  }
  .inputContainer {
    margin: variables(gutter) * 0.5;
    margin-top: variables(xLargeGutter) * 0.5;
  }

  .checkboxGroupContainer {
    flex-direction: column-reverse;
  }
  .footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    flex-direction: row;
    border-top: 1px solid colors(techGray_10);
  }

  .bottomSheetTitleWrapper {
    padding: variables(gutter);
    padding-top: variables(gutter) * 0.25;
    border-bottom: 1px solid colors(techGray_10);
  }

  @media (min-width: breakpoints(tablet)) {
    .checkBoxGroup {
      padding: 12px;
    }

    .childrenWrapper {
      width: 400px;
    }

    .leftIcon {
      display: flex;
    }

    .listItem {
      justify-content: start;
      &:hover {
        background-color: colors(backgroundIconSecondary);
      }
      &_active {
        background-color: colors(darkBrand) !important;
      }
    }

    .checkboxGroupContainer {
    }

    .inputContainer {
      margin: calc(variables(largeGutter) - variables(gutter) / 2);
      margin-bottom: variables(gutter) * 0.5;
    }

    .stickyAutoCompleteOptions {
      position: sticky;
    }
    .buttonsWrapper {
      padding: variables(largeGutter);
    }
    .salaryPickerWrapper {
      padding: variables(xLargeGutter) * 0.5 variables(gutter) * 0.25;
    }
  }
}
