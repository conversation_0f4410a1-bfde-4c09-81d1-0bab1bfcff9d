import React from 'react';
import type { ForwardedRef, HTMLAttributes, ReactHTML } from 'react';
import classes from './Flex.component.module.scss';
import cnj from '../utils/cnj';

type Directions = 'column' | 'row';

export interface FlexProps<ASType extends keyof ReactHTML = 'div'>
  extends HTMLAttributes<ReactHTML[ASType]> {
  as?: ASType;
  ref?: ForwardedRef<ReactHTML[ASType]>;
  alignItems?: string;
  flexDir?:
    | Directions
    | {
        mobile?: Directions;
        tablet?: Directions;
        midDesktop?: Directions;
        desktop?: Directions;
      };
}

const Flex = <ASType extends keyof ReactHTML = 'div'>(
  props: FlexProps<ASType>
) => {
  const {
    flexDir,
    alignItems,
    className,
    as: HtmlTag = 'div',
    ref,
    wrapperClassName,
    ...rest
  } = props;
  const isRowDir = flexDir === 'row';
  const alignItemsCenter = alignItems === 'center';

  return (
    <HtmlTag
      ref={ref}
      {...rest}
      className={cnj(
        classes.container,
        isRowDir && classes.dirRow,
        alignItemsCenter && classes.alignItemsCenter,
        className
      )}
    />
  );
};

export default Flex;
