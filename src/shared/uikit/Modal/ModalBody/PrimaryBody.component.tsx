import React from 'react';
// import RateLimitAlertCard from '@shared/components/Organism/PlanRestrictionCard';
import cnj from 'shared/uikit/utils/cnj';
import BaseBody from '../BasicModal/Bodies/Base';
import type { ModalBodyBaseProps } from '../BasicModal/Bodies/Base';
// import { isBusinessApp } from '@shared/utils/getAppEnv';

export type ModalBodyProps = ModalBodyBaseProps;

const ModalBody = ({ children, className, ...rest }: ModalBodyProps) => (
  <BaseBody className={cnj(className)} {...rest}>
    {/*{isBusinessApp && <RateLimitAlertCard />}*/}
    {children}
  </BaseBody>
);
export default ModalBody;
