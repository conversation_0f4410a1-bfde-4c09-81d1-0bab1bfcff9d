import { useStore } from '@tanstack/react-store';
import { Store } from '@tanstack/store';
import { useEffect } from 'react';
import eventKeys from '@shared/constants/event-keys';
import event from '@shared/utils/toolkit/event';
import type { PlanRestrictionPayload } from '@shared/types/planRestriction';

type PlanRestrictionStoreState = {
  events: PlanRestrictionPayload[];
};

// exceededFeature: 'SEARCH_COMPANIES',
// planAllow: true, // Always true, it means user has plan or not (free plan is the init)
// featureType: 'CALL_RATE_LIMITING',
// featureValue: null, // for each featureType it has different value;
// planName: 'FREE',

const planRestrictionStore = new Store<PlanRestrictionStoreState>({
  events: [],
});

// Action to add new event
const addPlanRestrictionEvent = (payload: PlanRestrictionPayload) => {
  planRestrictionStore.setState((state) => ({
    events: [...state.events, payload],
  }));
};

// Action to clear all events
const clearPlanRestrictionEvents = () => {
  planRestrictionStore.setState({ events: [] });
};

// Action to remove event by index
const removePlanRestrictionEvent = (index: number) => {
  planRestrictionStore.setState((state) => ({
    events: state.events.filter((_, i) => i !== index),
  }));
};

// Main hook
const usePlanRestriction = () => {
  // Use store in component
  const events = useStore(planRestrictionStore, (state) => state.events);

  useEffect(() => {
    // Event handler
    const handler = ((ev: CustomEvent) => {
      const payload = ev.detail as PlanRestrictionPayload;

      // Basic validation
      if (payload && typeof payload === 'object') {
        addPlanRestrictionEvent(payload);
      }
    }) as EventListener;

    // Listen to event
    event.on(eventKeys.planRestrictionsApplied, handler);

    // Cleanup
    return () => {
      event.off?.(eventKeys.planRestrictionsApplied, handler);
    };
  }, []);

  // Helper functions
  const helpers = {
    // Get all events
    getEvents: () => events,

    // Get latest event
    getLatestEvent: () => events[events.length - 1] || null,

    // Get events by feature
    getEventsByFeature: (feature: string) =>
      events.filter((event) => event.exceededFeature === feature),

    // Get events count
    getEventsCount: () => events.length,

    // Clear all events
    clearEvents: clearPlanRestrictionEvents,

    // Remove event by index
    removeEvent: removePlanRestrictionEvent,

    // Check if has event for feature
    hasEventForFeature: (feature: string) =>
      events.some((event) => event.exceededFeature === feature),
  };

  return {
    events,
    ...helpers,
  };
};

export default usePlanRestriction;

export {
  planRestrictionStore,
  addPlanRestrictionEvent,
  clearPlanRestrictionEvents,
  removePlanRestrictionEvent,
};
