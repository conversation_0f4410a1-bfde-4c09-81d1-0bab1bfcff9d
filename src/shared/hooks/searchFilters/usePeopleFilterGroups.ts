import { useQueryClient } from '@tanstack/react-query';
import uniqBy from 'lodash/uniqBy';
import { useSearchParams } from 'next/navigation';
import { useCommonFilterGroups } from '@shared/hooks/searchFilters/useCommonFilterGroups';
import { db, jobsDb } from '@shared/utils/constants/enums';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import geoApi from 'shared/utils/api/geo';
import { QueryKeys } from 'shared/utils/constants';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { makeCityValue } from 'shared/utils/makeCityValue';
import hereApiResponseNormalizer from 'shared/utils/normalizers/hereApiResponseNormalizer';
import lookupResponseNormalizer from 'shared/utils/normalizers/lookupResponseNormalizer';
import collectionToObjectByKey from 'shared/utils/toolkit/collectionToObjectByKey';
import useDynamicFilters from '../useDynamicFilters';
import classes from './useSearchFiltersFields.module.scss';

const { jobSearchOptions } = jobsDb;

const searchEntitiesObject = collectionToObjectByKey(
  jobSearchOptions.searchEntities
);

export const usePeopleFilterGroups = () => {
  const searchEntity = 'people';
  const { t } = useTranslation();
  const getQueryValue = useGetNormalizedArrayQuery();
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();
  const dynamicFilters = useDynamicFilters();
  const { PAGE_NUMBER, QUERY, SORT_BY } = useCommonFilterGroups();

  const searchGroupType =
    searchParams.get(searchFilterQueryParams.searchGroupType) ||
    searchGroupTypes.ALL;

  const countryCode = searchParams.get(searchFilterQueryParams.countryCode) as
    | string
    | null;
  const categoryIds = searchParams.getAll(searchFilterQueryParams.categoryIds);

  const categories = (() => {
    const _categories = dynamicFilters?.categories || [];
    const popularCategories =
      queryClient
        .getQueryData<any>([QueryKeys.jobPopularCategories])
        ?.content?.map((item: any) => ({
          value: item?.id,
          label: item?.title,
          image: item?.imageUrl,
        })) || [];
    const newCategories: any[] = [];
    (categoryIds as string[])?.forEach((id: string) => {
      if (_categories?.includes((_item: any) => _item?.value === id)) return;
      const popular = popularCategories?.find(
        (_item: any) => _item?.value === id
      );
      if (popular) newCategories.push(popular);
    });

    return uniqBy([..._categories, ...newCategories], 'value');
  })();

  const SEARCH_ENTITY = {
    name: searchFilterQueryParams.searchEntity,
    cp: 'list',
    options: jobSearchOptions.searchEntities,
    hiddenInHeader: false,
    alwaysShowInHeader: true,
    hiddenInForm: true,
    getValue: () => searchEntity,
    label: searchEntitiesObject[searchEntity]?.label,
    divider: { className: classes.groupDivider },
  };

  const SEARCH_GROUP_TYPE_USER = {
    name: searchFilterQueryParams.searchGroupType,
    cp: 'radioGroup',
    options: jobSearchOptions.searchGroupType,
    hiddenInHeader: true,
    hiddenInForm: true,
    getValue: () => searchGroupType,
    divider: { className: classes.groupDivider },
  };

  const MEMBER_SINCE = {
    formGroup: {
      color: 'smoke_coal',
      title: t('member_since'),
      className: classes.header,
    },
    cp: 'radioGroup',
    name: searchFilterQueryParams.memberSince,
    divider: { className: classes.groupDivider },
    options: dynamicFilters?.memberSince || [],
    label: t('member_since'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.memberSince) || 'ANY_TIME',
    hiddenInHeader: false,
    alwaysShowInHeader: true,
    isDefaultValue:
      !getQueryValue(searchFilterQueryParams.memberSince) ||
      getQueryValue(searchFilterQueryParams.memberSince) === 'ANY_TIME',
  };

  const CATEGORY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('category'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.categoryIds,
    divider: { className: classes.groupDivider },
    options: categories,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.getIndustry,
      normalizer: lookupResponseNormalizer,
    },
    label: t('category'),
    placeholder: t('search_category'),
    getValue: () => getQueryValue(searchFilterQueryParams.categoryIds, 'array'),
    alwaysShowInHeader: true,
  };

  const LOCATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('location'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.cities,
    options: dynamicFilters?.cities || [],
    label: t('locations'),
    placeholder: t('search_location'),
    getValue: () => getQueryValue(searchFilterQueryParams.cities, 'array'),
    alwaysShowInHeader: true,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      apiFunc: geoApi.suggestCity,
      params: { countryCode },
      normalizer: (items: any) =>
        items?.map((item: any) => ({
          label: item?.label,
          value: makeCityValue(item?.label, item?.cityCode),
        })),
    },
  };

  const LANGUAGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('languages'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    name: searchFilterQueryParams.languageIds,
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    label: t('languages'),
    placeholder: t('search_language'),
    options: dynamicFilters?.languages || [],
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.getLanguages,
      normalizer: lookupResponseNormalizer,
    },
    getValue: () => getQueryValue(searchFilterQueryParams.languageIds, 'array'),
    alwaysShowInHeader: true,
  };

  const PAGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('page'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.pageIds,
    divider: { className: classes.groupDivider },
    options: dynamicFilters?.pages || [],
    label: t('page'),
    placeholder: t('search_company'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
    },
    params: { countryCode },
    hiddenInBusiness: true,
    getValue: () => getQueryValue(searchFilterQueryParams.pageIds, 'array'),
  };

  const SCHOOLS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('schools'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.educatedAtSchoolIds,
    divider: { className: classes.groupDivider },
    options: dynamicFilters?.pages || [],
    label: t('school'),
    placeholder: t('search_school'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
    },
    params: { countryCode },
    hiddenInBusiness: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.educatedAtSchoolIds, 'array'),
  };

  const DGREES = dynamicFilters?.benefits?.length && {
    formGroup: {
      color: 'smoke_coal',
      title: t('degree'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.educationDegree,
    options: dynamicFilters?.benefits || [],
    label: t('degree'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.educationDegree, 'array'),
  };

  const groups = [
    PAGE_NUMBER,
    {
      name: searchFilterQueryParams.scope,
      formGroup: {
        color: 'smoke_coal',
        title: t('post_type'),
        className: classes.header,
      },
      cp: 'radioGroup',
      options: db.SCOPE,
      label: t('scope'),
      hiddenInHeader: true,
      hiddenInForm: true,
      getValue: () => getQueryValue(searchFilterQueryParams.scope) || 'ALL',
    },
    QUERY,
    SEARCH_ENTITY,
    SORT_BY,
    SEARCH_GROUP_TYPE_USER,
    MEMBER_SINCE,
    {
      ...CATEGORY,
      options: dynamicFilters?.industries || [],
      name: searchFilterQueryParams.industryIds,
      getValue: () =>
        getQueryValue(searchFilterQueryParams.industryIds, 'array'),
      alwaysShowInHeader: true,
    },
    LOCATION,
    LANGUAGES && { ...LANGUAGES, alwaysShowInHeader: true },
    {
      ...PAGES,
      formGroup: {
        color: 'smoke_coal',
        title: t('related_with'),
        className: classes.header,
      },
      label: t('related_with'),
      hiddenInHeader: false,
      alwaysShowInHeader: true,
      getValue: () =>
        getQueryValue(searchFilterQueryParams.relatedPageIds, 'array'),
      name: searchFilterQueryParams.relatedPageIds,
      options: dynamicFilters?.relatedPages || [],
    },
    DGREES,
  ];

  return groups.filter(Boolean);
};
