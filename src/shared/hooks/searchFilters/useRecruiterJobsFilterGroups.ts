'use client';

import { useCommonFilterGroups } from '@shared/hooks/searchFilters/useCommonFilterGroups';
import useDynamicFilters from '@shared/hooks/useDynamicFilters';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import { hashtagNormalizer } from '@shared/utils/normalizers/hashtagNormalizer';
import { searchFilterQueryParams } from 'shared/constants/search';
import geoApi from 'shared/utils/api/geo';
import {
  Endpoints,
  pageEndpoints,
  searchEndPoints,
} from 'shared/utils/constants';
import {
  experienceLevels,
  JOB_EMPLOYMENT_TYPE_MODEL,
  JOB_PRIORITY_MODEL,
  JOB_RESPONSE_TIME_MODEL,
  JOB_STATUS,
  WORK_SPACE_MODEL,
} from 'shared/utils/constants/enums/jobsDb';
import useTranslation from 'shared/utils/hooks/useTranslation';
import lookupResponseNormalizer, {
  lookupResponseNormalizerWithLabel,
} from 'shared/utils/normalizers/lookupResponseNormalizer';
import skillsResponseNormalizer from 'shared/utils/normalizers/skillsResponseNormalizer';
import classes from './useSearchFiltersFields.module.scss';
import type { PaginateResponse } from 'shared/types/response';

const FORM_GROUP_CLASS = '!py-0 !mb-12';
const PLUS_BUTTON_CLASS = '!mt-8 !ml-8';

export const useRecruiterJobsFilterGroups = () => {
  const dynamicFilters = useDynamicFilters();
  const { t } = useTranslation();
  const { authUser } = useGetAppObject();
  const countryCode = authUser?.location?.countryCode;
  const getQueryValue = useGetNormalizedArrayQuery();
  const { PAGE_NUMBER, QUERY, SORT_BY, DATE_POSTED } = useCommonFilterGroups();

  const STATUS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('status'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'radioGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.status,
    options: JOB_STATUS,
    label: t('status'),
    getValue: () => getQueryValue(searchFilterQueryParams.status),
    divider: { className: classes.groupDivider },
  };

  const TITLES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('j_title'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.titles,
    options: dynamicFilters.titles,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.getOccupations,
      normalizer: lookupResponseNormalizerWithLabel,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('j_title'),
    placeholder: t('search_j_title'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.titles, 'array'),
    divider: { className: classes.groupDivider },
  };

  const CATEGORY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('category'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.categoryIds,
    options: dynamicFilters.categories,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.getIndustry,
      normalizer: lookupResponseNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('category'),
    placeholder: t('search_category'),
    getValue: () => getQueryValue(searchFilterQueryParams.categoryIds, 'array'),
    divider: { className: classes.groupDivider },
  };

  const JOB_MODEL = {
    formGroup: {
      color: 'smoke_coal',
      title: t('job_model'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.workPlaceTypes,
    options: WORK_SPACE_MODEL,
    label: t('job_model'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.workPlaceTypes, 'array'),
    divider: { className: classes.groupDivider },
  };

  const EMPLOYMENT_TYPE = {
    formGroup: {
      color: 'smoke_coal',
      title: t('j_type'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.employmentTypes,
    options: JOB_EMPLOYMENT_TYPE_MODEL,
    label: t('j_type'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.employmentTypes, 'array'),
    divider: { className: classes.groupDivider },
  };
  const LOCATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('location'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.cities,
    options: dynamicFilters.cities,
    label: t('locations'),
    placeholder: t('search_location'),
    getValue: () => getQueryValue(searchFilterQueryParams.cities, 'array'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      apiFunc: geoApi.suggestCity,
      params: { countryCode },
      normalizer: (items: any) =>
        items?.map((item: any) => ({
          label: item?.label,
          value: item?.cityCode,
        })),
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    divider: { className: classes.groupDivider },
  };

  const PRIORITY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('priority'),
      className: FORM_GROUP_CLASS,
    },
    label: t('priority'),
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.priorities,
    options: JOB_PRIORITY_MODEL,
    getValue: () => getQueryValue(searchFilterQueryParams.priorities, 'array'),
    divider: { className: classes.groupDivider },
  };

  const SKILLS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('skills'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    name: searchFilterQueryParams.skills,
    options: dynamicFilters.skills,
    label: t('skills'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.getSkills,
      normalizer: skillsResponseNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    placeholder: t('search_skill'),
    getValue: () => getQueryValue(searchFilterQueryParams.skills, 'array'),
    alwaysShowInHeader: false,
    divider: { className: classes.groupDivider },
  };

  const LANGUAGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('languages'),
      className: FORM_GROUP_CLASS,
    },
    name: searchFilterQueryParams.languageIds,
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    label: t('languages'),
    placeholder: t('search_language'),
    options: dynamicFilters.languages,
    asyncAutoCompleteProps: {
      plusButtonClassName: PLUS_BUTTON_CLASS,
      maxLength: 100,
      url: Endpoints.App.Common.getLanguages,
      normalizer: lookupResponseNormalizer,
    },
    getValue: () => getQueryValue(searchFilterQueryParams.languageIds, 'array'),
    divider: { className: classes.groupDivider },
  };

  const EXPERIENCE_LEVEL = {
    formGroup: {
      color: 'smoke_coal',
      title: t('exp_level'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.experienceLevels,
    options: experienceLevels,
    label: t('exp_level'),
    placeholder: t('search_exp_level'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.experienceLevels, 'array'),
    divider: { className: classes.groupDivider },
  };

  const PROJECTS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('projects'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.projectIds,
    options: dynamicFilters.projects,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('projects'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.projectIds, 'array'),
    divider: { className: classes.groupDivider },
  };

  const SALARY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('salary_range'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'salaryPicker',
    withConfirmation: false,
    name: searchFilterQueryParams.salaryRange,
    data: dynamicFilters?.salaryRange ?? {},
    getValue: () => getQueryValue(searchFilterQueryParams.salaryRange),
    label: t('salary_range'),
    divider: { className: classes.groupDivider },
  };

  const COLLABORATORS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('assignees'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.collaboratorUserIds,
    options: dynamicFilters.collaborators,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: pageEndpoints.pageAccessibilities,
      normalizer: responseCollaboratorsNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('assignees'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.collaboratorUserIds, 'array'),
    divider: { className: classes.groupDivider },
  };

  const POINT_OF_CONTACT = {
    formGroup: {
      color: 'smoke_coal',
      title: t('point_of_contact'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.pointOfContactUserIds,
    options: dynamicFilters.pointOfContacts,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: pageEndpoints.pageAccessibilities,
      normalizer: responseCollaboratorsNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('point_of_contact'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.pointOfContactUserIds, 'array'),
    divider: { className: classes.groupDivider },
  };

  const CREATED_BY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('created_by'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.creatorUserIds,
    options: dynamicFilters.creators,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: pageEndpoints.pageAccessibilities,
      normalizer: responseCollaboratorsNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('created_by'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.creatorUserIds, 'array'),
    divider: { className: classes.groupDivider },
  };

  const RESPONSE_TIME = {
    formGroup: {
      color: 'smoke_coal',
      title: t('response_time'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.responseTimes,
    options: JOB_RESPONSE_TIME_MODEL,
    label: t('response_time'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.responseTimes, 'array'),
    divider: { className: classes.groupDivider },
  };

  const AUTHORIZATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('work_authorization'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.workAuthorizationIds,
    options: dynamicFilters.workAuthorization,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.searchAuthorization,
      normalizer: lookupResponseNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('work_authorization'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.workAuthorizationIds, 'array'),
    divider: { className: classes.groupDivider },
  };

  const TAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('tags'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.tags,
    options: dynamicFilters.tags,
    label: t('tags'),
    getValue: () => getQueryValue(searchFilterQueryParams.tags, 'array'),
    divider: { className: classes.groupDivider },
  };

  const HASHTAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('hashtags'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.hashtags,
    options: dynamicFilters.hashtags,
    label: t('hashtags'),
    placeholder: t('search_hashtag'),
    getValue: () => getQueryValue(searchFilterQueryParams.hashtags, 'array'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: searchEndPoints.suggestHashtag,
      normalizer: hashtagNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    divider: { className: classes.groupDivider },
  };

  const VENDOR = {
    formGroup: { color: 'smoke_coal', title: t('Vendor') },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.vendorIds,
    options: dynamicFilters.vendorIds,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    label: t('Vendor'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.vendorIds, 'array'),
    divider: { className: classes.groupDivider },
  };

  const CLIENT = {
    formGroup: { color: 'smoke_coal', title: t('Client') },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.clientIds,
    options: dynamicFilters.clientIds,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    label: t('Client'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.clientIds, 'array'),
    divider: { className: classes.groupDivider },
  };

  const TRAVEL_REQUIREMENTS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('willing_to_travel'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.travelRequirements,
    options: dynamicFilters.travelRequirements,
    label: t('willing_to_travel'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.travelRequirements, 'array'),
    divider: { className: classes.groupDivider },
  };

  const groups = [
    PAGE_NUMBER,
    QUERY,
    SORT_BY,
    DATE_POSTED,
    PROJECTS,
    STATUS,
    PRIORITY,
    TITLES,
    CATEGORY,
    JOB_MODEL,
    EMPLOYMENT_TYPE,
    LOCATION,
    VENDOR,
    CLIENT,
    SKILLS,
    LANGUAGES,
    EXPERIENCE_LEVEL,
    SALARY,
    COLLABORATORS,
    POINT_OF_CONTACT,
    CREATED_BY,
    RESPONSE_TIME,
    AUTHORIZATION,
    TAGS,
    HASHTAGS,
    TRAVEL_REQUIREMENTS,
  ];

  return groups.filter(Boolean);
};

type LookupResponseNormalizerType = Array<{ value: string; label: string }>;

export const responseProjectsNormalizer = (
  response: PaginateResponse<{ id: string; title: string }>
): LookupResponseNormalizerType =>
  response?.content
    ?.map(({ title, id }) => ({ value: id, label: title }))
    .slice(0, 10);

export const responseCollaboratorsNormalizer = (
  response: PaginateResponse<{
    userId: string;
    profileInfo: { fullName: string };
  }>
): LookupResponseNormalizerType =>
  response?.content
    ?.map(({ profileInfo, userId }) => ({
      value: userId,
      label: profileInfo.fullName,
    }))
    .slice(0, 6);
