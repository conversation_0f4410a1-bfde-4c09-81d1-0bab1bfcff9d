@import '/src/shared/theme/theme.scss';

@layer molecule {
  .header {
    background-color: transparent;
    margin-bottom: variables(xLargeGutter) * 0.5;
    padding-top: 0;
    padding-bottom: 0;
  }
  .groupDivider {
    margin: variables(largeGutter)
      calc((variables(largeGutter) - variables(xLargeGutter) / 2));
  }
  .plusButtonClassName {
    margin-left: variables(gutter) * 0.5;
  }
  .checkBoxInputContainer {
    margin: variables(gutter) * 0.5;
    margin-bottom: 0;
  }
  .benefitsContainer {
    flex-direction: column-reverse;
  }
}
