import { useSearchParams } from 'next/navigation';
import { useCommonFilterGroups } from '@shared/hooks/searchFilters/useCommonFilterGroups';
import { jobsDb } from '@shared/utils/constants';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import collectionToObjectByKey from '@shared/utils/toolkit/collectionToObjectByKey';
import { searchFilterQueryParams } from 'shared/constants/search';
import geoApi from 'shared/utils/api/geo';
import { suggestObjects } from 'shared/utils/api/search';
import Endpoints from 'shared/utils/constants/endpoints';
import { searchEndPoints } from 'shared/utils/constants/servicesEndpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { makeCityValue } from 'shared/utils/makeCityValue';
import { hashtagNormalizer } from 'shared/utils/normalizers/hashtagNormalizer';
import hereApiResponseNormalizer from 'shared/utils/normalizers/hereApiResponseNormalizer';
import useDynamicFilters from '../useDynamicFilters';
import classes from './useSearchFiltersFields.module.scss';

const { jobSearchOptions } = jobsDb;
const searchEntitiesObject = collectionToObjectByKey(
  jobSearchOptions.searchEntities
);

export const usePostsFilterGroups = () => {
  const { t } = useTranslation();
  const searchEntity = 'posts';
  const searchParams = useSearchParams();
  const dynamic = useDynamicFilters();
  const getQueryValue = useGetNormalizedArrayQuery();
  const countryCode = searchParams.get(searchFilterQueryParams.countryCode);
  const datePosted = dynamic?.datePosted || [];
  const postTypes = dynamic?.postTypes || [];
  const postedByOptions = dynamic?.postedBy || [];
  const cities = dynamic?.cities || [];
  const creators = dynamic?.creators || [];
  const mentions = dynamic?.mentions || [];
  const hashtags = dynamic?.hashtags || [];
  const { PAGE_NUMBER, QUERY, SORT_BY } = useCommonFilterGroups();

  const SEARCH_ENTITY = {
    name: searchFilterQueryParams.searchEntity,
    cp: 'list',
    options: jobSearchOptions.searchEntities,
    hiddenInHeader: false,
    alwaysShowInHeader: true,
    hiddenInForm: true,
    getValue: () => searchEntity,
    label: searchEntitiesObject[searchEntity]?.label,
    divider: { className: classes.groupDivider },
  };

  const POST_TYPE = {
    formGroup: {
      color: 'smoke_coal',
      title: t('post_type'),
      className: classes.header,
    },
    cp: 'radioGroup',
    name: searchFilterQueryParams.postType,
    divider: { className: classes.groupDivider },
    options: postTypes,
    label: t('post_type'),
    alwaysShowInHeader: true,
    getValue: () => getQueryValue(searchFilterQueryParams.postType),
  };

  const LOCATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('location'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.cities,
    options: cities,
    label: t('locations'),
    placeholder: t('search_location'),
    getValue: () => getQueryValue(searchFilterQueryParams.cities, 'array'),
    alwaysShowInHeader: true,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      apiFunc: geoApi.suggestCity,
      params: { countryCode },
      normalizer: (items: any) =>
        items?.map((item: any) => ({
          label: item?.label,
          value: makeCityValue(item?.label, item?.cityCode),
        })),
    },
  };

  const CREATED_BY = {
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    divider: { className: classes.groupDivider },
    options: creators,
    placeholder: t('search_creator'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
    },
    alwaysShowInHeader: true,
    formGroup: {
      color: 'smoke_coal',
      title: t('creator'),
      className: classes.header,
    },
    label: t('creator'),
    name: searchFilterQueryParams.postedByUserIds,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.postedByUserIds, 'array'),
  };

  const POSTED_BY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('posted_by'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    cp: 'checkBoxGroup',
    name: searchFilterQueryParams.postedBy,
    options: postedByOptions,
    label: t('posted_by'),
    getValue: () => getQueryValue(searchFilterQueryParams.postedBy, 'array'),
    alwaysShowInHeader: true,
  };

  const MENTIONS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('mentions'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.mentionedUserIds,
    divider: { className: classes.groupDivider },
    options: mentions,
    label: t('mentions'),
    placeholder: t('search_mentions'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.mentionedUserIds, 'array'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      apiFunc: suggestObjects,
      normalizer: (response: any) =>
        response?.content?.map(
          ({ id, fullName, type, usernameAtSign }: any) => ({
            value: id,
            label: fullName,
            type,
            helperText: usernameAtSign,
          })
        ),
    },
  };

  const HASHTAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('hashtags'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.hashtags,
    options: hashtags,
    label: t('hashtags'),
    placeholder: t('search_hashtag'),
    getValue: () => getQueryValue(searchFilterQueryParams.hashtags, 'array'),
    alwaysShowInHeader: true,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: searchEndPoints.suggestHashtag,
      normalizer: hashtagNormalizer,
    },
  };

  const groups = [
    PAGE_NUMBER,
    QUERY,
    SEARCH_ENTITY,
    SORT_BY,
    DATE_POSTED,
    POST_TYPE,
    LOCATION,
    CREATED_BY,
    POSTED_BY,
    MENTIONS,
    HASHTAGS,
  ];

  return groups.filter(Boolean);
};
