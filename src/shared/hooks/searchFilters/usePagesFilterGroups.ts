import { useSearchParams } from 'next/navigation';
import { jobsDb } from '@shared/utils/constants/enums';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import geoApi from 'shared/utils/api/geo';
import Endpoints from 'shared/utils/constants/endpoints';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { makeCityValue } from 'shared/utils/makeCityValue';
import lookupResponseNormalizer from 'shared/utils/normalizers/lookupResponseNormalizer';
import collectionToObjectByKey from 'shared/utils/toolkit/collectionToObjectByKey';
import useDynamicFilters from '../useDynamicFilters';
import { useCommonFilterGroups } from './useCommonFilterGroups';
import classes from './useSearchFiltersFields.module.scss';

const { jobSearchOptions } = jobsDb;
const searchEntitiesObject = collectionToObjectByKey(
  jobSearchOptions.searchEntities
);

export const usePagesFilterGroups = () => {
  const searchEntity = 'pages';
  const { t } = useTranslation();
  const getQueryValue = useGetNormalizedArrayQuery();
  const searchParams = useSearchParams();
  const dynamicFilters = useDynamicFilters();
  const { PAGE_NUMBER, QUERY, SORT_BY } = useCommonFilterGroups();

  const searchGroupType =
    searchParams.get(searchFilterQueryParams.searchGroupType) ||
    searchGroupTypes.ALL;

  const countryCode = searchParams.get(searchFilterQueryParams.countryCode) as
    | string
    | null;

  const SEARCH_ENTITY = {
    name: searchFilterQueryParams.searchEntity,
    cp: 'list',
    options: jobSearchOptions.searchEntities,
    hiddenInHeader: false,
    alwaysShowInHeader: true,
    hiddenInForm: true,
    getValue: () => searchEntity,
    label: searchEntitiesObject[searchEntity]?.label,
    divider: { className: classes.groupDivider },
  };

  const SEARCH_GROUP_TYPE_USER = {
    name: searchFilterQueryParams.searchGroupType,
    cp: 'radioGroup',
    options: jobSearchOptions.searchGroupType,
    hiddenInHeader: true,
    hiddenInForm: true,
    getValue: () => searchGroupType,
    divider: { className: classes.groupDivider },
  };

  const ESTABLISHMENT_DATE = {
    formGroup: {
      color: 'smoke_coal',
      title: t('establishment_date'),
      className: classes.header,
    },
    label: t('establishment_date'),
    cp: 'radioGroup',
    name: searchFilterQueryParams.establishmentDate,
    divider: { className: classes.groupDivider },
    options: dynamicFilters?.establishmentDate || [],
    getValue: () =>
      getQueryValue(searchFilterQueryParams.establishmentDate) || 'ANY_TIME',
    hiddenInHeader: false,
    alwaysShowInHeader: true,
    isDefaultValue:
      !getQueryValue(searchFilterQueryParams.establishmentDate) ||
      getQueryValue(searchFilterQueryParams.establishmentDate) === 'ANY_TIME',
  };

  const CATEGORY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('category'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.categoryIds,
    divider: { className: classes.groupDivider },
    options: dynamicFilters?.industries || [],
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.getIndustry,
      normalizer: lookupResponseNormalizer,
    },
    label: t('category'),
    placeholder: t('search_category'),
    getValue: () => getQueryValue(searchFilterQueryParams.categoryIds, 'array'),
    alwaysShowInHeader: true,
  };

  const PAGE_TYPES = dynamicFilters?.pageTypes?.length && {
    formGroup: {
      color: 'smoke_coal',
      title: t('page_type'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.categories,
    label: t('page_type'),
    divider: { className: classes.groupDivider },
    options: dynamicFilters?.pageTypes,
    getValue: () => getQueryValue(searchFilterQueryParams.categories, 'array'),
    alwaysShowInHeader: true,
  };

  const LOCATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('location'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.cities,
    options: dynamicFilters?.cities || [],
    label: t('locations'),
    placeholder: t('search_location'),
    getValue: () => getQueryValue(searchFilterQueryParams.cities, 'array'),
    alwaysShowInHeader: true,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      apiFunc: geoApi.suggestCity,
      params: { countryCode },
      normalizer: (items: any) =>
        items?.map((item: any) => ({
          label: item?.label,
          value: makeCityValue(item?.label, item?.cityCode),
        })),
    },
  };

  const COMPANY_SIZE = dynamicFilters?.companySizes?.length && {
    formGroup: {
      color: 'smoke_coal',
      title: t('page_size'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.companySizes,
    label: t('page_size'),
    divider: { className: classes.groupDivider },
    options: dynamicFilters?.companySizes,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.companySizes, 'array'),
    alwaysShowInHeader: true,
  };

  const LANGUAGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('languages'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    name: searchFilterQueryParams.languageIds,
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    label: t('languages'),
    placeholder: t('search_language'),
    options: dynamicFilters?.languages || [],
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.getLanguages,
      normalizer: lookupResponseNormalizer,
    },
    getValue: () => getQueryValue(searchFilterQueryParams.languageIds, 'array'),
    alwaysShowInHeader: true,
  };

  const HASHTAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('hashtags'),
      className: classes.header,
    },
    divider: { className: classes.groupDivider },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.hashtags,
    options: dynamicFilters?.hashtags || [],
    label: t('hashtags'),
    placeholder: t('search_hashtag'),
    getValue: () => getQueryValue(searchFilterQueryParams.hashtags, 'array'),
    alwaysShowInHeader: false,
  };

  const groups = [
    PAGE_NUMBER,
    QUERY,
    SEARCH_ENTITY,
    SORT_BY,
    SEARCH_GROUP_TYPE_USER,
    ESTABLISHMENT_DATE,
    CATEGORY,
    PAGE_TYPES,
    LOCATION,
    COMPANY_SIZE,
    LANGUAGES,
    HASHTAGS,
  ];

  return groups.filter(Boolean);
};
