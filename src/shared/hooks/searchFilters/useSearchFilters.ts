import { useQueryClient } from '@tanstack/react-query';
import uniqBy from 'lodash/uniqBy';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback } from 'react';
import eventKeys from 'shared/constants/event-keys';
import { mutableStore } from 'shared/constants/mutableStore';
import {
  searchGroupTypes,
  searchFilterQueryParams,
} from 'shared/constants/search';
import { useSearchDispatch } from 'shared/contexts/search/search.provider';
import encoder from 'shared/utils/toolkit/encoder';
import event from 'shared/utils/toolkit/event';
import isEmptyObjectValues from 'shared/utils/toolkit/isEmptyObjectValues';
import useDynamicFilters from '../useDynamicFilters';
import type {
  SearchFiltersQueryParamsType,
  SearchFiltersValueType,
} from 'shared/types/search';

const useSearchFilters = () => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const dynamicFilters = useDynamicFilters();
  const searchDispatch = useSearchDispatch();
  const queryClient = useQueryClient();
  const searchGroupType =
    searchParams.get(searchFilterQueryParams.searchGroupType) ||
    searchGroupTypes.ALL;
  const occupationId = searchParams.get(searchFilterQueryParams.occupationId);
  const currentEntityId = searchParams.get(
    searchFilterQueryParams.currentEntityId
  );

  const isLocationTitleFiltered = searchParams.get(
    searchFilterQueryParams.isLocationTitleFiltered
  );

  const searchFilters = useCallback(
    (groups) => {
      const qParams = groups?.reduce((prev: any, curr: any) => {
        const val = curr.getValue();
        const isEmpty = isEmptyObjectValues({
          val,
        });

        return isEmpty
          ? prev
          : {
              ...prev,
              [curr.name]: decodeURIComponent(val),
            };
      }, {});

      qParams.occupationId = occupationId ?? undefined;
      qParams.currentEntityId = currentEntityId ?? undefined;
      qParams.isLocationTitleFiltered = isLocationTitleFiltered ?? undefined;

      return qParams;
    },
    [occupationId, currentEntityId, isLocationTitleFiltered]
  );

  const setFilters = useCallback(
    (variables: Record<any, any>, removeCurrentEntityId?: boolean) => {
      const obj = { ...variables } as Record<
        SearchFiltersQueryParamsType,
        SearchFiltersValueType
      >;

      Object.keys(obj)?.forEach((_key) => {
        const prop = _key as SearchFiltersQueryParamsType;
        obj[prop] = getValue(obj[prop], prop);
      });

      const _searchFilters = { ...obj } as any;
      if (
        !obj[searchFilterQueryParams.searchGroupType] ||
        obj[searchFilterQueryParams.searchGroupType] !== searchGroupTypes.ALL
      ) {
        _searchFilters[searchFilterQueryParams.searchGroupType] =
          searchGroupTypes.ALL;
      }
      if (!obj[searchFilterQueryParams.currentEntityId]) {
        _searchFilters[searchFilterQueryParams.currentEntityId] = undefined;
        _searchFilters[searchFilterQueryParams.page] = 0;
      }
      if (obj[searchFilterQueryParams.page]) {
        _searchFilters[searchFilterQueryParams.page] = 0;
      }

      const _searchGroupType =
        _searchFilters[searchFilterQueryParams.searchGroupType] ||
        searchGroupType;

      const isSameCategory =
        _searchFilters[searchFilterQueryParams.searchGroupType] ===
        searchFilters[searchFilterQueryParams.searchGroupType];

      if (
        !!_searchGroupType &&
        _searchGroupType !== searchGroupTypes.ALL &&
        !isLocationTitleFiltered &&
        !isSameCategory
      ) {
        _searchFilters[searchFilterQueryParams.locationTitle] = undefined;
        _searchFilters[searchFilterQueryParams.countryCode] = undefined;
        _searchFilters[searchFilterQueryParams.cityCode] = undefined;
        _searchFilters[searchFilterQueryParams.stateCode] = undefined;
        _searchFilters[searchFilterQueryParams.countryId] = undefined;
      }

      if (removeCurrentEntityId) {
        _searchFilters[searchFilterQueryParams.currentEntityId] = undefined;
      }
      _searchFilters[searchFilterQueryParams.query] = encoder(
        _searchFilters[searchFilterQueryParams.query] || ''
      );
      const search = new URLSearchParams(
        JSON.parse(JSON.stringify(_searchFilters))
      ).toString();
      router.push(`${pathname}?${search}`);
    },
    [isLocationTitleFiltered, pathname, router, searchFilters, searchGroupType]
  );

  const setFilter = useCallback(
    (key: string, value: any, isReplace?: boolean) => {
      let _searchFilters = { ...searchFilters };
      _searchFilters = { ..._searchFilters, [key]: getValue(value) };
      if (key !== searchFilterQueryParams.currentEntityId) {
        _searchFilters[searchFilterQueryParams.currentEntityId] = undefined;
        if (key !== searchFilterQueryParams.page) {
          _searchFilters[searchFilterQueryParams.page] = 0;
        }
      }

      if (
        key !== searchFilterQueryParams.searchGroupType &&
        key !== searchFilterQueryParams.currentEntityId &&
        key !== searchFilterQueryParams.page
      ) {
        _searchFilters[searchFilterQueryParams.searchGroupType] =
          searchGroupTypes.ALL;
      }

      const _searchGroupType =
        _searchFilters[searchFilterQueryParams.searchGroupType] ||
        searchGroupType;

      const isSameCategory =
        _searchFilters[searchFilterQueryParams.searchGroupType] ===
        searchFilters[searchFilterQueryParams.searchGroupType];

      if (
        !!_searchGroupType &&
        _searchGroupType !== searchGroupTypes.ALL &&
        !isLocationTitleFiltered &&
        !isSameCategory
      ) {
        _searchFilters[searchFilterQueryParams.locationTitle] = undefined;
        _searchFilters[searchFilterQueryParams.countryCode] = undefined;
        _searchFilters[searchFilterQueryParams.cityCode] = undefined;
        _searchFilters[searchFilterQueryParams.stateCode] = undefined;
        _searchFilters[searchFilterQueryParams.countryId] = undefined;
      }

      if (key !== searchFilterQueryParams.currentEntityId) {
        _searchFilters[searchFilterQueryParams.currentEntityId] = undefined;
      }

      _searchFilters[searchFilterQueryParams.query] = encoder(
        _searchFilters[searchFilterQueryParams.query] || ''
      );
      const search = new URLSearchParams(
        JSON.parse(JSON.stringify(_searchFilters))
      ).toString();
      if (isReplace) {
        router.replace(`${pathname}?${search}`);
      } else {
        router.push(`${pathname}?${search}`);
      }
    },
    [searchFilters]
  );

  const resetFilters = useCallback(() => {
    // Do not remove the inputs
    const filters = {
      [searchFilterQueryParams.query]:
        searchFilters[searchFilterQueryParams.query],
      [searchFilterQueryParams.locationTitle]:
        searchFilters[searchFilterQueryParams.locationTitle],
      [searchFilterQueryParams.countryCode]:
        searchFilters[searchFilterQueryParams.countryCode],
      [searchFilterQueryParams.cityCode]:
        searchFilters[searchFilterQueryParams.cityCode],
      [searchFilterQueryParams.stateCode]:
        searchFilters[searchFilterQueryParams.stateCode],
    };
    queryClient.invalidateQueries(mutableStore.searchQueryKey);
    const search = new URLSearchParams(
      JSON.parse(JSON.stringify(filters))
    ).toString();
    router.push(`${pathname}?${search}`);
  }, []);

  const resetFilter = useCallback(
    (key: string) => {
      setFilter(key, undefined);
    },
    [setFilter]
  );

  const addToDynamicFilters = useCallback(
    (variables: any) => {
      const _dynamicFilters = { ...dynamicFilters };
      if (Object.keys(dynamicFilters || {})) {
        Object?.keys(_dynamicFilters)?.forEach((key) => {
          if (!(key in variables)) return;
          variables[key] = variables[getDynamicFilterEquivalentName(key)];
          const oldValues = dynamicFilters[key];
          const newValues = variables[key];
          if (Array.isArray(newValues) && newValues?.length > 0) {
            _dynamicFilters[key] = uniqBy(
              [...newValues, ...(oldValues || [])],
              'value'
            );
          }
        });
      }

      searchDispatch({
        type: 'SET_SEARCH_DYNAMIC_FILTERS',
        payload: _dynamicFilters,
      });

      return variables;
    },
    [dynamicFilters, searchDispatch]
  );

  const addToDynamicFiltersAndSetFilter = useCallback(
    (variables: any) => {
      const vars = addToDynamicFilters(variables);
      setFilters(vars);
    },
    [setFilters]
  );

  const refetchWithCurrentEntityId = () => {
    event.trigger(eventKeys.refetchSearchWithCurrentEntityId);
  };

  return {
    searchFilters,
    resetFilters,
    resetFilter,
    setFilters,
    setFilter,
    addToDynamicFiltersAndSetFilter,
    getLabelOrValue,
    refetchWithCurrentEntityId,
  };
};

export default useSearchFilters;

function getDynamicFilterEquivalentName(name: string): string {
  if (name === 'categories') return 'categoryIds';
  if (name === 'pages') return 'pageIds';
  if (name === 'languages') return 'languageIds';

  return name;
}

function getValue(
  val: any,
  prop?: SearchFiltersQueryParamsType
): string | string[] {
  if (typeof val === 'string') return val as string;
  if (Array.isArray(val)) {
    const _prop = getLabelOrValue(prop as any);

    return val?.map((item) => item[_prop] || item?.id || item?.value || item);
  }

  return val?.value || val;
}

function getLabelOrValue(
  itemName: SearchFiltersQueryParamsType
): 'label' | 'value' {
  if (['skills', 'titles'].includes(itemName)) return 'label';

  return 'value';
}
