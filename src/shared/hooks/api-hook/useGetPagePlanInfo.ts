import pageApi from 'shared/utils/api/page';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';

export type PagePlanInfoResponse = {
  enrolledPlanInfoList: Array<{
    planName: string;
    portalName: string;
    timeSpan: string;
  }>;
  paidPerCallFeatureInfoList: Array<{
    featureName: string;
    price: number;
  }>;
};

const useGetPagePlanInfo = (config = {}) =>
  useReactQuery<PagePlanInfoResponse>({
    action: {
      key: [QueryKeys.getPagePlanInfo],
      apiFunc: pageApi.getPagePlanInfo,
    },
    config,
  });

export default useGetPagePlanInfo;