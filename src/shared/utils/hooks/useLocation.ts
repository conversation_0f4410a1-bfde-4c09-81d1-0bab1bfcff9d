import { useParams, usePathname, useSearchParams } from 'next/navigation';

const useLocation = () => {
  const pathname = usePathname();
  const params = useParams();
  const path = `/${pathname.split('/').filter(Boolean)[0]}`;
  const searchParams = useSearchParams();
  const entireSearchString = new URLSearchParams(searchParams).toString();

  const withSearchParams = (url?: string, queryParams?: URLSearchParams) => {
    const queryString = queryParams?.size ? `?${queryParams.toString()}` : '';
    return url ? `${url}${queryString}` : queryString;
  };

  const preserveSearchParams = (
    url?: string,
    config?: { append?: Record<string, string>; delete: string[] }
  ) => {
    const newSearchParams = new URLSearchParams(searchParams);
    if (config?.delete?.length)
      config.delete.forEach((key) => {
        newSearchParams.delete(key);
      });
    if (config?.append)
      Object.entries(config.append).forEach(([key, value]) => {
        newSearchParams.set(key, value);
      });

    return withSearchParams(url, newSearchParams);
  };
  return {
    pathname,
    params,
    searchParams,
    path,
    search: entireSearchString ? '?'.concat(entireSearchString) : '',
    withSearchParams,
    preserveSearchParams,
  };
};

export default useLocation;
