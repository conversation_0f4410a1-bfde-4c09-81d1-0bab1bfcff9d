import type {
  BatchAddCandidateNoteRequest,
  BECandidateNote,
  CandidateNoteRequest,
  ICandidateNote,
} from '@shared/types/candidates';
import { normalizeCandidateNote } from '@shared/utils/normalizers/beforeCacheCandidateInfo';
import sortByKey from '@shared/utils/toolkit/date';
import { candidateEndpoints } from '../../constants/servicesEndpoints';
import request from '../../toolkit/request';
import { PaginateResponse } from '@shared/types/response';
import { NoteVisibilityType } from '@shared/utils/constants/enums/candidateDb';

export const getCandidatesNotes = async (params: {
  candidateId?: string | number;
  page?: number;
  size?: number;
  text?: string;
  visibility?: NoteVisibilityType;
  onlyDone?: boolean;
}) => {
  const { data } = await request.get<PaginateResponse<BECandidateNote>>(
    candidateEndpoints.searchNote,
    { params }
  );
  const { content, ...rest } = data;

  return {
    ...rest,
    content: sortByKey(content, 'createdDate').map(normalizeCandidateNote),
  };
};

export const addCandidateNote = async (
  candidateId: string,
  body: CandidateNoteRequest
): Promise<BECandidateNote> => {
  const url = candidateEndpoints.singleNoteById(candidateId);
  const { data } = await request.post<BECandidateNote>(url, body);
  return data;
};

export const editCandidateNote = async (
  noteId: string,
  body: CandidateNoteRequest
) => {
  const url = candidateEndpoints.singleNoteById(noteId);
  return await request.put<BECandidateNote>(url, body);
};

export const removeCandidateNote = async (
  noteId: string
): Promise<BECandidateNote> => {
  const url = candidateEndpoints.singleNoteById(noteId);
  const { data } = await request.delete<BECandidateNote>(url);
  return data;
};

export const batchAddCandidateNote = async (
  params: BatchAddCandidateNoteRequest
) => {
  const { data } = await request.post(candidateEndpoints.batchAddNote, params);
  return data;
};
