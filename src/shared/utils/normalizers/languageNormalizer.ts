import type { Language } from '@shared/types/language';
import { db } from 'shared/utils/constants/enums';

const { LANGUAGE_LEVELS } = db;

export type LanguageNormalizerType = {
  id: string;
  name: string;
  originalId?: string;
  languageLookupId: string | number;
  level: any;
  recruiterData?: boolean;
};

const languageNormalizer = (item: LanguageNormalizerType): Language => {
  const { id, name, languageLookupId, level, originalId, recruiterData } = item;
  const levelIndex = LANGUAGE_LEVELS.findIndex((i) => i.value === level);
  const progress = levelIndex + 1;
  const levelLabel =
    LANGUAGE_LEVELS?.[levelIndex]?.label ?? LANGUAGE_LEVELS[0].label;
  const realData = {
    name: { label: name, value: languageLookupId },
    id,
    originalId,
    level: {
      value: level,
      label: levelLabel,
    },
    progress,
  };

  // const invalidLookupId = Number(languageLookupId) > 0;

  return {
    id,
    name,
    level: levelLabel,
    realData,
    progress,
    label: name,
    languageLevel: level,
    recruiterData,
  };
};

export default languageNormalizer;
