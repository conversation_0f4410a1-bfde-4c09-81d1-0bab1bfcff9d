import type { BESkill } from '@shared/types/skill';
import { SKILL_LEVELS } from '../constants/enums/db';

const skillNormalizer = (item: BESkill): Skill => {
  const { id, level, name, skillLookupId, type, originalId, recruiterData } =
    item;
  const levelIndex = SKILL_LEVELS.findIndex((i) => i.value === level);
  const progress = levelIndex + 1;
  const levelLabel = SKILL_LEVELS?.[levelIndex]?.label;
  const realData = {
    name: { label: name, type, value: skillLookupId },
    id,
    level: {
      value: level,
      label: levelLabel,
    },
    originalId,
    progress,
    type,
  };

  return {
    id,
    name,
    level: levelLabel,
    realData,
    progress,
    label: name,
    type,
    skillLevel: level,
    recruiterData,
  };
};

export default skillNormalizer;
