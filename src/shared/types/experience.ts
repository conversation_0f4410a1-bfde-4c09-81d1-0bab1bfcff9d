import type { WorkPlaceType } from '@shared/types/pipelineTypes';
import type { BELocation } from 'shared/types/lookup';

export type Experience = {
  cityLookupId: string;
  companyName: string;
  companyPageId: string;
  createdDate: string;
  currentlyWorking: boolean;
  description: string;
  employmentType: string;
  endDate: string;
  id: string;
  lastModifiedDate: string;
  locationDescription: string;
  occupationLookupId: string;
  occupationName: string;
  originalId?: string;
  startDate: string;
  version: string;
  volunteer: boolean;
  pageCroppedImageUrl: string;
  location?: BELocation;
  cause?: string;
  workPlaceType?: WorkPlaceType;
  workPlaceTypeLabel?: string;
  recruiterData?: boolean;
};
