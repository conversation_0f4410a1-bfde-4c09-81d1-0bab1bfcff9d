'use client';

import { FallbackText } from '@shared/components/atoms/FallbackText';
import { usePagesFilterGroups } from '@shared/hooks/searchFilters/usePagesFilterGroups';
import useSearchResultWithFilters from '@shared/hooks/searchFilters/useSearchResultWithFilters';
import BaseButton from '@shared/uikit/Button/BaseButton';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import useMedia from '@shared/uikit/utils/useMedia';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import SearchListWithDetailsLayout from 'shared/components/layouts/SearchListWithDetailsLayout';
import SearchCard from 'shared/components/Organism/SearchCard';
import SearchList from 'shared/components/Organism/SearchList';
import pagesCategories from 'shared/constants/pagesCategories';
import { searchGroupTypes } from 'shared/constants/search';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SearchPagesDetails from './SearchPages.details';
import type { JSX } from 'react';

const SearchPages = (): JSX.Element => {
  const { t } = useTranslation();
  const { allParams } = useCustomParams();
  const { searchGroupType = searchGroupTypes.ALL } = allParams;
  const { isMoreThanTablet } = useMedia();

  const {
    totalElements,
    totalPages,
    content = [],
    isEmpty,
    setPage,
    isFetching,
    isFetchingFilters,
  } = useSearchResultWithFilters({ entity: 'pages' });
  const groups = usePagesFilterGroups();
  const { handleChangeParams } = useCustomParams();

  const titles = {
    [searchGroupTypes.ALL]: t('pages'),
    [searchGroupTypes.TOP_SUGGESTION]: t('top_suggestions'),
    [searchGroupTypes.POPULAR]: t('popular_pages'),
    [searchGroupTypes.FOLLOWERS]: t('your_followers'),
    [searchGroupTypes.FOLLOWINGS]: t('your_following'),
    [searchGroupTypes.INCOMING_REQUESTS]: t('incoming_requests'),
  } as any;

  const title = titles[searchGroupType];

  return (
    <SearchListWithDetailsLayout
      groups={groups}
      isLoading={isFetchingFilters}
      isTotalyEmpty={Boolean(isEmpty && !isFetching)}
      sectionTotalEmpty={
        <EmptySectionInModules
          isFullParent
          isFullWidth
          title={t('no_result_f')}
          text={t('try_refining_search')}
        />
      }
      listComponent={
        <SearchList
          entity="pages"
          title={title}
          isLoading={isFetching}
          totalElements={totalElements}
          data={content}
          totalPages={totalPages}
          onPageChange={setPage as any}
          noTopBottomPadding
          noItemButtonAction={isMoreThanTablet}
          renderItem={(item, index, props) => (
            <BaseButton
              onClick={() =>
                handleChangeParams({ add: { currentEntityId: item.id } })
              }
            >
              <SearchCard
                imgSrc={item.croppedImageUrl}
                firstText={item.title}
                secondText={`@${item.username}`}
                fourthText={<FallbackText value={item.locationTitle} />}
                thirdText={t(pagesCategories[item.category]?.label)}
                {...props}
                isPage
              />
            </BaseButton>
          )}
        />
      }
      detailsComponent={<SearchPagesDetails isLoading={isFetching} />}
    />
  );
};

export default SearchPages;
