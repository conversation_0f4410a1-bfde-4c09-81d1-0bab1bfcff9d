'use client';

import { FeaturePriceButton } from '@shared/components/molecules/FeaturePriceButton';
import ProjectCardInList from '@shared/components/molecules/ProjectCard/ProjectCardInList';
import { useRecruiterProjectsFilterGroups } from '@shared/hooks/searchFilters/useRecruiterProjectsFilterGroups';
import useSearchResultWithFilters from '@shared/hooks/searchFilters/useSearchResultWithFilters';
import { FeatureName } from '@shared/types/planRestriction';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import SearchListWithDetailsLayout from 'shared/components/layouts/SearchListWithDetailsLayout';
import SearchList from 'shared/components/Organism/SearchList';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import EmptySearchSvg from 'shared/svg/EmptySearchSvg';
import DividerVertical from 'shared/uikit/Divider/DividerVertical';
import Box from 'shared/uikit/Layout/Box';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './loading.module.scss';
import RecruiterProjectDetailsWrapper from './partials/RecruiterProjects/RecruiterProjectDetailsWrapper';
import type { JobAPIProps } from '@shared/types/jobsProps';

const Page = () => {
  const { handleChangeParams, allParams } = useCustomParams();
  const { currentEntityId } = allParams;
  const groups = useRecruiterProjectsFilterGroups();
  const { t } = useTranslation();

  const {
    totalElements,
    totalPages,
    content = [] as JobAPIProps[],
    isEmpty,
    setPage,
    isFetching,
    isFetchingFilters,
  } = useSearchResultWithFilters({
    entity: 'recruiterProjects',
  });

  const handleOpenCreateModal = () =>
    openMultiStepForm({
      formName: 'createProjectForm',
    });

  return (
    <SearchListWithDetailsLayout
      isFullWidth
      groups={groups}
      isLoading={isFetchingFilters}
      isTotalyEmpty={Boolean(isEmpty && !isFetching)}
      headerComponents={
        <>
          <DividerVertical />
          <FeaturePriceButton
            featureName={FeatureName.PROJECT_CREATION}
            label={t('create')}
            leftIcon="plus"
            disabled={isFetching || isFetchingFilters}
            onClick={handleOpenCreateModal}
          />
        </>
      }
      listComponent={
        <SearchList
          entity="recruiterProjects"
          title={t('projects')}
          isLoading={isFetching}
          totalElements={totalElements}
          data={content}
          onPageChange={setPage}
          totalPages={totalPages}
          noItemButtonAction
          renderItem={(project) => (
            <ProjectCardInList
              item={project}
              key={`job_${project.id}`}
              isFocused={currentEntityId === project.id}
              classNames={{ root: classes.projectItem }}
              onClick={() =>
                handleChangeParams({ add: { currentEntityId: project.id } })
              }
            />
          )}
          scrollToTopWhenClick
        />
      }
      detailsComponent={
        <RecruiterProjectDetailsWrapper
          projectId={currentEntityId}
          parentLoading={isFetching}
        />
      }
      hasBackBtn={false}
      sectionTotalEmpty={
        <EmptySectionInModules
          title={t('empty_projects')}
          text={t('empty_projects_description')}
          image={
            <Box style={{ maxWidth: '280px' }}>
              <EmptySearchSvg />
            </Box>
          }
          buttonProps={{
            title: t('create_project'),
            onClick: handleOpenCreateModal,
            leftIcon: 'plus',
          }}
          classNames={{ container: classes.emptySection }}
        />
      }
    />
  );
};

export default Page;
