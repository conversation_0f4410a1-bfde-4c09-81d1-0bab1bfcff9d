import React from 'react';
import { JobElementProvider } from '@app/jobs/partials/context/JobElement/JobElement.provider';
import JobCard from '@shared/components/molecules/JobCard';
import { useJobsFilterGroups } from '@shared/hooks/searchFilters/useJobsFilterGroups';
import BaseButton from '@shared/uikit/Button/BaseButton';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import useMedia from '@shared/uikit/utils/useMedia';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import SearchListWithDetailsLayout from 'shared/components/layouts/SearchListWithDetailsLayout';
import SearchList from 'shared/components/Organism/SearchList';
import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import useSearchResultWithFilters from '@shared/hooks/searchFilters/useSearchResultWithFilters';
import DividerVertical from 'shared/uikit/Divider/DividerVertical';
import Flex from 'shared/uikit/Flex';
import { jobStatusKeys } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SearchJobsDetails from './SearchJobs.details';

const SearchJobsAuth: React.FC = () => {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const { handleChangeParams, allParams } = useCustomParams();
  const { currentEntityId, searchGroupType = searchGroupTypes.ALL } = allParams;

  const titles = {
    [searchGroupTypes.ALL]: allParams.query || t('jobs'),
    [searchGroupTypes.CREATED_BY]: allParams.query || t('jobs'),
    [searchGroupTypes.TOP_SUGGESTION]: t('top_suggestions'),
    [searchGroupTypes.POPULAR]: t('popular_jobs'),
    [searchGroupTypes.APPLIED]: (
      <Flex flexDir="row">
        {t('applied')} <DividerVertical className="!m-0_8" />
        {t(allParams.jobGroupStatus)}
      </Flex>
    ),
    [searchGroupTypes.SAVED]: (
      <Flex flexDir="row">
        {t('saved')} <DividerVertical className="!m-0_8" />
        {t(allParams.jobGroupStatus)}
      </Flex>
    ),
  } as any;
  const title = titles[searchGroupType];

  const extraParams = allParams.jobGroupStatus
    ? allParams.jobGroupStatus === jobStatusKeys.hired
      ? { isHired: true }
      : allParams.jobGroupStatus === jobStatusKeys.applied
        ? { isApplied: true }
        : { status: allParams.jobGroupStatus }
    : searchGroupType === searchGroupTypes.CREATED_BY
      ? { [searchFilterQueryParams.pageIds]: allParams.pageIds }
      : {};

  const groups = useJobsFilterGroups();

  const {
    totalElements,
    totalPages,
    content = [],
    isEmpty,
    setPage,
    queryKey,
    isFetching,
    isFetchingFilters,
  } = useSearchResultWithFilters({ entity: 'jobs', extraParams });

  return (
    <SearchListWithDetailsLayout
      groups={groups}
      isLoading={isFetchingFilters}
      isTotalyEmpty={Boolean(isEmpty && !isFetching)}
      sectionTotalEmpty={
        <EmptySectionInModules
          isFullParent
          isFullWidth
          title={t('no_result_f')}
          text={t('try_refining_search')}
        />
      }
      searchInputDefaultView="text"
      listComponent={
        <SearchList
          entity="jobs"
          title={title}
          isLoading={isFetching}
          totalElements={totalElements}
          data={content}
          onPageChange={setPage}
          totalPages={totalPages}
          locationTitle={allParams?.locationTitle}
          noTopBottomPadding
          noItemButtonAction={isMoreThanTablet}
          renderItem={(job, index, props) => (
            <BaseButton
              onClick={() =>
                handleChangeParams({ add: { currentEntityId: job.id } })
              }
            >
              <JobCard
                variant="listItem"
                job={job}
                queryKey={queryKey}
                {...props}
              />
            </BaseButton>
          )}
        />
      }
      detailsComponent={
        <JobElementProvider
          initialValue={{
            queryKey,
            jobId: currentEntityId,
            mode: 'edit',
          }}
        >
          <SearchJobsDetails
            currentEntityId={currentEntityId}
            isFetching={isFetching}
          />
        </JobElementProvider>
      }
    />
  );
};

export default SearchJobsAuth;
