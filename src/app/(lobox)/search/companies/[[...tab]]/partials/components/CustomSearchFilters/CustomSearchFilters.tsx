import React, { useRef, useState } from 'react';
import SearchFilters, {
  type SearchFiltersProps,
} from '@shared/components/layouts/SearchListWithDetailsLayout/SearchFilters';
import SearchFilterIcon from '@shared/components/molecules/SearchFilterIcon';
import { useSearchDispatch } from '@shared/contexts/search/search.provider';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import { CompanyTab } from '@shared/types/company';
import { InvitationEntityType } from '@shared/types/invitation';
import Button from '@shared/uikit/Button';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Flex from '@shared/uikit/Flex';
import Tabs from '@shared/uikit/Tabs';
import { getPendingAndRequestCount } from '@shared/utils/api/company';
import { routeNames } from '@shared/utils/constants';
import QueryKeys from '@shared/utils/constants/queryKeys';
import useLocation from '@shared/utils/hooks/useLocation';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useGetCompanyActiveTab from '../../hooks/useGetCompanyActiveTab';
import classes from './index.module.scss';

const CustomSearchFilters = (props: SearchFiltersProps) => {
  const tabsRef = useRef(null);
  const { t } = useTranslation();
  const [showFilters, setShowFilters] = useState(false);
  const activeTab = useGetCompanyActiveTab();
  const searchDispatch = useSearchDispatch();
  const { preserveSearchParams } = useLocation();
  const { data } = useReactQuery({
    action: {
      apiFunc: getPendingAndRequestCount,
      key: [QueryKeys.getPendingAndRequestCount],
    },
  });

  const tabs = [
    {
      defaultPath: preserveSearchParams(undefined, {
        delete: ['currentEntityId'],
      }),
      path: routeNames.companies.search,
      exact: true,
      title: t('all'),
    },
    {
      defaultPath: preserveSearchParams(undefined, {
        delete: ['currentEntityId'],
      }),
      path: routeNames.companies.vendors,
      title: t('vendors'),
    },
    {
      defaultPath: preserveSearchParams(undefined, {
        delete: ['currentEntityId'],
      }),
      path: routeNames.companies.clients,
      title: t('clients'),
    },
    {
      defaultPath: preserveSearchParams(undefined, {
        delete: ['currentEntityId'],
      }),
      path: routeNames.companies.requests,
      title: t('requests'),
      badge: data?.request,
    },
    {
      defaultPath: preserveSearchParams(undefined, {
        delete: ['currentEntityId'],
      }),
      path: routeNames.companies.pending,
      title: t('pending'),
      badge: data?.pending,
    },
  ];

  const handleAddCompany = (type: string) => () => {
    searchDispatch({
      type: 'SET_ADD_COMPANY_MODAL_DATA',
      payload: {
        isOpen: 'true',
        companyType: type,
      },
    });
  };
  const openInviteModal = () =>
    openMultiStepForm({
      formName: 'invitePeople',
      data: { entity: InvitationEntityType.COMPANY, initialMethod: 'emails' },
    });

  if (showFilters)
    return (
      <SearchFilters
        {...props}
        hasBackBtn
        onBackHandler={() => {
          setShowFilters(false);
        }}
      />
    );

  return (
    <Tabs
      linkVariant="badge"
      ref={tabsRef}
      tabs={tabs}
      isFullWidth
      linkAndActionWrapperClassName={classes.topPageFiltersWrapper}
      tabsWrapperClassName={classes.tabsWrapperClassName}
      actionButton={
        <Flex className={classes.actionsWrapper}>
          <DividerVertical className="!mx-8" />
          <Button
            label={t('filters')}
            schema="semi-transparent"
            shape="capsule"
            leftIcon="sliders-simple"
            onClick={() => {
              setShowFilters(true);
            }}
          />
          {activeTab === CompanyTab.VENDORS ? (
            <Flex className="!flex-row gap-12 ml-auto">
              <Button
                label={t('invite')}
                schema="semi-transparent"
                leftIcon="invite"
                onClick={openInviteModal}
              />
              <Button
                label={t('add_vendor')}
                leftIcon="plus"
                onClick={handleAddCompany(CompanyTab.VENDORS)}
              />
            </Flex>
          ) : activeTab === CompanyTab.CLIENTS ? (
            <Flex className="!flex-row gap-12 ml-auto">
              <Button
                label={t('invite')}
                schema="semi-transparent"
                leftIcon="invite"
                onClick={openInviteModal}
              />
              <Button
                label={t('add_client')}
                leftIcon="plus"
                onClick={handleAddCompany(CompanyTab.CLIENTS)}
              />
            </Flex>
          ) : (
            <SearchFilterIcon wrapperClassName="ml-auto" />
          )}
        </Flex>
      }
    />
  );
};

export default CustomSearchFilters;
