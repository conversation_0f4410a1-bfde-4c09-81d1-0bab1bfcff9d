'use client';

import React from 'react';
import { FeaturePriceButton } from '@shared/components/molecules/FeaturePriceButton';
import { useRecruiterJobsFilterGroups } from '@shared/hooks/searchFilters/useRecruiterJobsFilterGroups';
import useSearchResultWithFilters from '@shared/hooks/searchFilters/useSearchResultWithFilters';
import EmptySearchSvg from '@shared/svg/EmptySearchSvg';
import { FeatureName } from '@shared/types/planRestriction';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import Box from '@shared/uikit/Layout/Box';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import SearchListWithDetailsLayout from 'shared/components/layouts/SearchListWithDetailsLayout';
import BusinessJobCardInList from 'shared/components/molecules/BusinessJobCard/BusinessJobCardInList';
import SearchList from 'shared/components/Organism/SearchList';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import DividerVertical from 'shared/uikit/Divider/DividerVertical';
import useTranslation from 'shared/utils/hooks/useTranslation';
import RecruiterJobDetails from './partials/RecruiterJobs/RecruiterJobDetails';
import type { JSX } from 'react';
import type { JobAPIProps } from 'shared/types/jobsProps';

const SearchPages = (): JSX.Element => {
  const { t } = useTranslation();
  const { handleChangeParams, allParams } = useCustomParams();
  const { currentEntityId } = allParams;
  const groups = useRecruiterJobsFilterGroups();

  const {
    totalElements,
    totalPages,
    content = [] as JobAPIProps[],
    isEmpty,
    setPage,
    isFetching,
    isFetchingFilters,
  } = useSearchResultWithFilters({
    entity: 'recruiterJobs',
  });

  const handleOpenCreateModal = () =>
    openMultiStepForm({
      formName: 'createJobForm',
    });

  return (
    <SearchListWithDetailsLayout
      isFullWidth
      groups={groups}
      isLoading={isFetchingFilters}
      isTotalyEmpty={Boolean(isEmpty && !isFetching)}
      headerComponents={
        <>
          <DividerVertical />
          <FeaturePriceButton
            featureName={FeatureName.JOB_CREATION}
            label={t('create')}
            disabled={isFetching || isFetchingFilters}
            leftIcon="plus"
            onClick={handleOpenCreateModal}
          />
        </>
      }
      listComponent={
        <SearchList
          entity="recruiterJobs"
          title={t('jobs')}
          isLoading={isFetching}
          totalElements={totalElements}
          data={content}
          onPageChange={setPage}
          totalPages={totalPages}
          noItemButtonAction
          renderItem={(job, index, props) => (
            <BusinessJobCardInList
              job={job}
              key={`job_${job.id}`}
              cardProps={{
                classNames: { root: '!mb-12' },
                isFocused: currentEntityId === job.id,
                onClick: () =>
                  handleChangeParams({ add: { currentEntityId: job.id } }),
              }}
            />
          )}
          scrollToTopWhenClick
        />
      }
      detailsComponent={
        <RecruiterJobDetails
          jobId={currentEntityId}
          parentLoading={isFetching}
        />
      }
      hasBackBtn={false}
      sectionTotalEmpty={
        <EmptySectionInModules
          title={t('no_jobs')}
          text={t('no_jobs_desc')}
          image={
            <Box style={{ maxWidth: '280px' }}>
              <EmptySearchSvg />
            </Box>
          }
          buttonProps={{
            title: t('create_job'),
            onClick: handleOpenCreateModal,
            leftIcon: 'plus',
          }}
          classNames={{ container: 'flex-1 !my-20' }}
        />
      }
    />
  );
};

export default SearchPages;
