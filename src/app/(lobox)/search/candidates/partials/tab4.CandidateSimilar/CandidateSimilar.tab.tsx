import { useRouter } from 'next/navigation';
import React, { useCallback, type FC } from 'react';
import CandidateCard from '@shared/components/molecules/CandidateCard';
import { IsManualWrapper } from '@shared/components/molecules/IsManualWrapper';
import SendMessageButton from '@shared/components/molecules/SendMessageButton';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import PlanRestrictionCard from '@shared/components/Organism/PlanRestrictionCard';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import { FeatureName } from '@shared/types/planRestriction';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex/index';
import { getSimilarCandidates } from '@shared/utils/api/candidates';
import { QueryKeys, routeNames } from '@shared/utils/constants';
import { useGetNormalizedArrayQuery } from '@shared/utils/hooks/useGetNormalizedArrayQuery';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import SimilarCandidatesSkeleton from './CandidateSimilar.skeleton';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import type { BaseCandidateSectionProp } from '@shared/types/candidates';

export const CandidateSimilarTab: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const getQueryValue = useGetNormalizedArrayQuery();
  const similarEntityId = getQueryValue('similarEntityId');
  const appDispatch = useGlobalDispatch();
  const router = useRouter();

  const { data: similarUsers, isLoading } = useReactInfiniteQuery<any>(
    [QueryKeys.getSimilarCandidates, candidate?.id],
    {
      func: getSimilarCandidates,
      size: 10,
      extraProps: {
        id: similarEntityId || candidate?.id,
      },
    }
  );

  const handleOpenManagerModal = useCallback(
    (candidateId: string, tab: CandidateManagerTabkeys) => {
      if (candidateId) {
        appDispatch({
          type: 'TOGGLE_CANDIDATE_MANAGER',
          payload: {
            isOpen: true,
            tab,
            id: candidateId,
            enableNavigate: false,
          },
        });
      }
    },
    [appDispatch]
  );

  return isLoading ? (
    <SimilarCandidatesSkeleton />
  ) : similarUsers.length > 0 ? (
    <Flex className="mt-32 gap-20">
      <PlanRestrictionCard featuresName={[FeatureName.SEE_SIMILAR_CANDIDATE]} />
      {similarUsers.map((user) => (
        <CandidateCard
          key={user.id}
          enableLinks
          secondText={user?.usernameAtSign}
          thirdText={user?.occupation?.label}
          fourthText={cleanRepeatedWords(user?.location?.title || '')}
          FirstTextWrapper={
            !candidate.profile.username ? IsManualWrapper : undefined
          }
          onBadgeClick={(tab) => handleOpenManagerModal(user.id, tab)}
          showBadges
          showTags
          showActions
          firstText={user.fullName}
          avatar={user.croppedImageUrl}
          onClick={() =>
            router.push(
              `${routeNames.searchCandidates}?currentEntityId=${user.id}`
            )
          }
        >
          <Flex className="!flex-row gap-12">
            <SendMessageButton
              className="flex-1"
              disabled={!user?.username}
              object={{
                id: user.originalId,
                croppedImageUrl: user.croppedImageUrl,
                fullName: user.fullName,
                username: user.username,
                isPage: false,
              }}
              fullWidth
            />
            <Button
              className="flex-1"
              label={t('manage')}
              leftIcon="user-cog"
              fullWidth
              onClick={() => handleOpenManagerModal(user.id, 'notes')}
            />
          </Flex>
        </CandidateCard>
      ))}
    </Flex>
  ) : (
    <Flex className="mt-32 gap-20 !flex-1">
      <EmptySearchResult
        title={t('no_similar_found')}
        sectionMessage={t('no_similar_candidate_found')}
        className="!py-32 px-0 !m-0"
      />
    </Flex>
  );
};
