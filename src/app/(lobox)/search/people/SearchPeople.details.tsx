import React, { useState } from 'react';
import SearchEntityInfo from '@app/search/partials/SearchObjectInfo';
import RightSearchResultSkeleton from '@app/search/partials/Skeletons/RightSearchResultSkeleton';
import SearchFilterSkeleton from '@app/search/partials/Skeletons/SearchFilterSkeleton';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import { profileSectionsStepKeys } from 'shared/components/Organism/MultiStepForm/ProfileSections/constants';
import ObjectFeed from 'shared/components/Organism/ObjectFeed';
import { SocialConnectionsTabs } from 'shared/constants/enums';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import useProfilePage from 'shared/hooks/useProfilePage';
import Flex from 'shared/uikit/Flex';
import useGetObjectDetail from 'shared/utils/hooks/useGetObjectDetail';
import useTranslation from 'shared/utils/hooks/useTranslation';
import preventClickHandler from 'shared/utils/toolkit/preventClickHandler';
import classes from './SearchPeople.details.module.scss';

interface SearchPeopleDetailsProps {
  isLoading: boolean;
  currentEntityId: string;
}

const SearchPeopleDetails: React.FC<SearchPeopleDetailsProps> = ({
  isLoading,
  currentEntityId,
}) => {
  const { t } = useTranslation();
  const dispatch = useGlobalDispatch();
  const { authUser } = useGetAppObject();
  const [initFilter, setIntFilter] = useState('all');

  const {
    isLoading: isFetching,
    data: objectDetail,
    objectDetailDataKey,
  } = useGetObjectDetail({
    objectId: currentEntityId,
  });
  const { isLoading: isLoadingProfilePage } = useProfilePage({
    objectId: currentEntityId,
  });

  const openObjectNetworkModal = (currentTab: string) => () => {
    dispatch({
      type: 'OPEN_OBJECT_NETWORK_MODAL',
      payload: {
        currentTab,
        objectUsername: objectDetail?.username,
        objectId: objectDetail?.id,
        objectTitle: objectDetail?.title || objectDetail?.fullName,
        network: objectDetail?.network,
        isPage: false,
      },
    });
  };
  const onClickPosts = (e: any) => {
    preventClickHandler(e);
    setIntFilter('posts');
  };

  const openRecommend = () => {
    openMultiStepForm({
      formName: 'editProfileSections',
      stepKey: profileSectionsStepKeys.RECOMMENDATION,
      data: {
        isSingle: true,
        activeState: 'add',
        initialSecondaryStep: 2,
        initialSelectedUser: {
          ...objectDetail,
          occupationName: objectDetail?.occupation?.label,
        },
      },
    });
  };

  if (isFetching || isLoading || !objectDetail || isLoadingProfilePage) {
    return <RightSearchResultSkeleton />;
  }

  return (
    <Flex className={classes.wrapper}>
      <SearchEntityInfo
        queryKey={objectDetailDataKey}
        data={{
          object: objectDetail,
          firstText: objectDetail?.fullName,
          secondText: objectDetail?.usernameAtSign,
          thirdText: objectDetail?.occupation?.label,
          fourthText: objectDetail?.location?.title,
          image: objectDetail?.croppedImageUrl,
          id: objectDetail?.id,
          network: objectDetail?.network,
          username: objectDetail?.username,
          isPage: false,
          isBlocked: objectDetail?.youHaveBlocked,
          bg: objectDetail?.croppedHeaderImageLink,
        }}
        followerData={[
          {
            title: t('followers_cap'),
            value: objectDetail?.network?.followersCounter || '0',
            onClick: openObjectNetworkModal(SocialConnectionsTabs.followers),
          },
          {
            title: t('following_cap'),
            value: objectDetail?.network?.followingsCounter || '0',
            onClick: openObjectNetworkModal(SocialConnectionsTabs.following),
          },
          {
            title: t('mutuals'),
            value: objectDetail?.mutualCounter || '0',
            onClick: openObjectNetworkModal(SocialConnectionsTabs.mutuals),
          },
          {
            title: t('posts'),
            value: objectDetail?.network?.postsCounter || '0',
            onClick: onClickPosts,
          },
        ]}
        openRecommend={openRecommend}
      />
      {objectDetail?.id && !objectDetail?.youHaveBlocked && (
        <ObjectFeed
          visibleCreatePost={false}
          objectId={objectDetail.id}
          fullName={objectDetail.fullName}
          isAuthUser={authUser?.id === objectDetail.id}
          isAuthBusinessPage={false}
          isFollowingFromOtherPlaces={objectDetail.network?.follow}
          showSideOnDesktop={false}
          initFilter={initFilter}
          variant="search"
          renderFilterSkeleton={
            isFetching ? (
              <SearchFilterSkeleton className={classes.within} />
            ) : null
          }
        />
      )}
      {objectDetail?.id && objectDetail?.youHaveBlocked && (
        <EmptySectionInModules
          title={t('y_blocked_t_profile')}
          text={t('i_y_t_t_mistake_un_user')}
          isFullParent
          classNames={{ container: classes.blockedContainer }}
        />
      )}
    </Flex>
  );
};

export default SearchPeopleDetails;
