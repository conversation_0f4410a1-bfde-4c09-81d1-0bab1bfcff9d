'use client';

import { FallbackText } from '@shared/components/atoms/FallbackText';
import { usePeopleFilterGroups } from '@shared/hooks/searchFilters/usePeopleFilterGroups';
import useSearchResultWithFilters from '@shared/hooks/searchFilters/useSearchResultWithFilters';
import BaseButton from '@shared/uikit/Button/BaseButton';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import useMedia from '@shared/uikit/utils/useMedia';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import SearchListWithDetailsLayout from 'shared/components/layouts/SearchListWithDetailsLayout';
import SearchCard from 'shared/components/Organism/SearchCard';
import SearchList from 'shared/components/Organism/SearchList';
import { searchGroupTypes } from 'shared/constants/search';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SearchPeopleDetails from './SearchPeople.details';
import type { JSX } from 'react';

const SearchPeople = (): JSX.Element => {
  const { t } = useTranslation();
  const { allParams } = useCustomParams();
  const { searchGroupType = searchGroupTypes.ALL, currentEntityId } = allParams;
  const {
    totalElements,
    totalPages,
    content = [],
    isEmpty,
    setPage,
    isFetching,
    isFetchingFilters,
  } = useSearchResultWithFilters({ entity: 'people' });
  const groups = usePeopleFilterGroups();
  const { handleChangeParams } = useCustomParams();
  const { isMoreThanTablet } = useMedia();

  const titles = {
    [searchGroupTypes.ALL]: t('people'),
    [searchGroupTypes.TOP_SUGGESTION]: t('top_suggestions'),
    [searchGroupTypes.POPULAR]: t('popular_people'),
    [searchGroupTypes.FOLLOWERS]: t('your_followers'),
    [searchGroupTypes.FOLLOWINGS]: t('your_following'),
    [searchGroupTypes.INCOMING_REQUESTS]: t('incoming_requests'),
    [searchGroupTypes.PENDING_REQUESTS]: t('pending_requests'),
  } as any;
  const title = titles[searchGroupType];

  return (
    <SearchListWithDetailsLayout
      groups={groups}
      isLoading={isFetchingFilters}
      isTotalyEmpty={Boolean(isEmpty && !isFetching)}
      sectionTotalEmpty={
        <EmptySectionInModules
          isFullParent
          isFullWidth
          title={t('no_result_f')}
          text={t('try_refining_search')}
        />
      }
      listComponent={
        <SearchList
          entity="people"
          title={title}
          isLoading={isFetching}
          totalElements={totalElements}
          data={content}
          totalPages={totalPages}
          onPageChange={setPage}
          noItemButtonAction={isMoreThanTablet}
          noTopBottomPadding
          renderItem={(item, index, props) => (
            <BaseButton
              onClick={() =>
                handleChangeParams({ add: { currentEntityId: item.id } })
              }
            >
              <SearchCard
                imgSrc={item.croppedImageUrl}
                firstText={item.fullName}
                secondText={`@${item.username}`}
                thirdText={item.occupationName}
                fourthText={<FallbackText value={item.locationTitle} />}
                {...props}
              />
            </BaseButton>
          )}
        />
      }
      detailsComponent={
        <SearchPeopleDetails
          currentEntityId={currentEntityId}
          isLoading={isFetching}
        />
      }
    />
  );
};

export default SearchPeople;
