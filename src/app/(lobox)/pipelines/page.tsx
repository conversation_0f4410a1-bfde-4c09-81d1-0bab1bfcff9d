'use client';

import debounce from 'lodash/debounce';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import PipelineCard from '@shared/components/molecules/PipelineCard';
import PipelineCardSkeleton from '@shared/components/molecules/PipelineCard/PipelineCardSkeleton';
import { useRecruiterJobsFilterGroups } from '@shared/hooks/searchFilters/useRecruiterJobsFilterGroups';
import useSearchResultWithFilters from '@shared/hooks/searchFilters/useSearchResultWithFilters';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import EmptySearchSvg from '@shared/svg/EmptySearchSvg';
import Button from '@shared/uikit/Button';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import Box from '@shared/uikit/Layout/Box';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import SearchListWithDetailsLayout from 'shared/components/layouts/SearchListWithDetailsLayout';
import SearchList from 'shared/components/Organism/SearchList';
import useTranslation from 'shared/utils/hooks/useTranslation';
import PipelineCardMoreOptions from './partials/Pipelines/PipelineCardMoreOptions';
import PipelinesListHeader from './partials/Pipelines/PipelinesListHeader';
import type { JobAPIProps } from '@shared/types/jobsProps';

const Pipelines = () => {
  const { t } = useTranslation();
  const { handleChangeParams, allParams } = useCustomParams();
  const router = useRouter();

  const [searchInput, setSearchInput] = useState(allParams.query || '');
  const [isBulk, setIsBulk] = useState(false);
  const [selectedPipelines, setSelectedPipelines] = useState<JobAPIProps[]>([]);

  const debounceFn = useCallback(
    debounce((text: string) => handleChangeParams({ add: { text } }), 500),
    []
  );

  const onSearchChange = (text: string) => {
    debounceFn(text);
    setSearchInput(text);
  };

  const groups = useRecruiterJobsFilterGroups();

  const {
    totalElements,
    totalPages,
    content = [] as JobAPIProps[],
    isEmpty,
    setPage,
    isFetching,
    isFetchingFilters,
  } = useSearchResultWithFilters({
    entity: 'pipelines',
  });

  const handleOpenCreateModal = () =>
    openMultiStepForm({
      formName: 'createJobForm',
    });

  const togglePipelineSelection = (pipeline: JobAPIProps) => {
    if (pipeline.status === 'UNPUBLISHED') return;

    if (selectedPipelines.includes(pipeline)) {
      setSelectedPipelines(selectedPipelines.filter((p) => p !== pipeline));
    } else {
      setSelectedPipelines([...selectedPipelines, pipeline]);
    }
  };

  const onOpenPipeline = (id: string) => router.push(`/pipelines/${id}`);

  return (
    <SearchListWithDetailsLayout
      groups={groups}
      isLoading={isFetchingFilters}
      isTotalyEmpty={Boolean(isEmpty && !isFetching)}
      classNames={{
        contentRoot: '!max-w-none',
        headerRoot: '!m-0 !max-w-none',
        emptyState: '!m-0 !max-w-none !px-0 !pr-[14px]',
      }}
      headerComponents={
        <Button
          label={t('create')}
          disabled={isFetching || isFetchingFilters}
          leftIcon="plus"
          onClick={handleOpenCreateModal}
        />
      }
      listComponent={
        <SearchList
          entity="recruiterJobs"
          title={t('pipelines')}
          showCustomComponent={isBulk}
          headerElement={
            <PipelinesListHeader
              isBulk={isBulk}
              setIsBulk={setIsBulk}
              selectedPipelines={selectedPipelines}
              searchInput={searchInput}
              onSearchChange={onSearchChange}
              clearSelectedPipelines={() => setSelectedPipelines([])}
            />
          }
          isLoading={isFetching}
          totalElements={totalElements}
          data={content}
          onPageChange={setPage}
          totalPages={totalPages}
          noItemButtonAction
          renderItem={(pipeline) => (
            <PipelineCard
              job={pipeline}
              key={`pipeline_${pipeline.id}`}
              cardProps={{
                classNames: { root: 'mb-20' },
                onClick: () =>
                  isBulk
                    ? togglePipelineSelection(pipeline)
                    : onOpenPipeline(pipeline.id),
              }}
              moreOptions={
                <PipelineCardMoreOptions
                  isBulk={isBulk}
                  isSelected={selectedPipelines.includes(pipeline)}
                  pipeline={pipeline}
                />
              }
            />
          )}
          scrollToTopWhenClick
          ItemSkeleton={() => PipelineCardSkeleton({ className: 'mb-20' })}
        />
      }
      hasBackBtn={false}
      hasFilterIcon={false}
      sectionTotalEmpty={
        <EmptySectionInModules
          title={t('no_data')}
          text={t('no_data')}
          image={
            <Box style={{ maxWidth: '280px' }}>
              <EmptySearchSvg />
            </Box>
          }
          classNames={{ container: 'flex-1 !my-20 !mr-[14px] ' }}
        />
      }
    />
  );
};

export default Pipelines;
