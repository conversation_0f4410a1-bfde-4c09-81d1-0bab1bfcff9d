const fs = require('fs');
const path = require('path');

const directoryPath = '../src/'; // Set your project directory path here

function replaceImportStatement(fileContent) {
  const importComponentRegex =
    /import {\s*([\w,\s]+)\s*} from 'shared\/uikit\/Modal';/gs;
  const importTypeRegex =
    /import type {\s*([\w,\s]+)\s*} from 'shared\/uikit\/Modal';/gs;

  const replaceComponents = (components) =>
    components
      .split(',')
      .map((component) => component.trim())
      .filter((component) => component.length > 0)
      .map(
        (trimmedComponent) =>
          `import ${trimmedComponent} from 'shared/uikit/Modal/${trimmedComponent}';`
      )
      .join('\n');

  const replaceTypes = (types) =>
    types
      .split(',')
      .map((type) => type.trim())
      .filter((type) => type.length > 0)
      .map(
        (trimmedType) =>
          `import type ${trimmedType} from 'shared/uikit/Modal/${trimmedType}';`
      )
      .join('\n');

  fileContent = fileContent.replace(importComponentRegex, (_, components) =>
    replaceComponents(components)
  );
  return fileContent.replace(importTypeRegex, (_, types) =>
    replaceTypes(types)
  );
}

function processFile(filePath) {
  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      console.error(`Error reading file ${filePath}:`, err);
      return;
    }

    const updatedContent = replaceImportStatement(data);

    fs.writeFile(filePath, updatedContent, 'utf8', (err) => {
      if (err) {
        console.error(`Error writing file ${filePath}:`, err);
      } else {
        console.log(`Processed file: ${filePath}`);
      }
    });
  });
}

function processDirectory(directory) {
  fs.readdir(directory, { withFileTypes: true }, (err, files) => {
    if (err) {
      console.error(`Error reading directory ${directory}:`, err);
      return;
    }

    files.forEach((file) => {
      const fullPath = path.join(directory, file.name);
      if (file.isDirectory()) {
        processDirectory(fullPath);
      } else if (
        file.isFile() &&
        !file.name.endsWith('.old.ts') &&
        !file.name.endsWith('.old.tsx')
      ) {
        processFile(fullPath);
      }
    });
  });
}

processDirectory(directoryPath);
