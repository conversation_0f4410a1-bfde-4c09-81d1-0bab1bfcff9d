const logErrors = require('./logErrors');

function cleanUpStyles(styles) {
  styles = styles
    .replace(/const useStyles =/, '')
    .replace(/createUseStyles\(([\s\S]*?)\);/, '$1');
  styles = styles.trim();

  // Handle callbacks that return objects like () => ({}) or ({ variables, breakpoints }) => ({})
  if (styles.startsWith('()') || styles.startsWith('({')) {
    return styles;
    // try {
    //   // Attempt to parse as JSON
    //   const parsedStyles = JSON.parse(styles);
    //   return JSON.stringify(parsedStyles, null, 2);
    // } catch (error) {
    //   console.log(logErrors[3]);
    //   return styles;
    // }
  }

  return styles;
}

module.exports = cleanUpStyles;
