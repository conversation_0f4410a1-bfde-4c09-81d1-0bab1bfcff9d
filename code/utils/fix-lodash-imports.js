const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

class LodashImportFixer {
  constructor(options = {}) {
    this.options = {
      rootDir: options.rootDir || process.cwd(),
      extensions: options.extensions || ['ts', 'tsx', 'js', 'jsx'],
      dryRun: options.dryRun || false,
      verbose: options.verbose || false,
      excludeDirs: options.excludeDirs || [
        'node_modules',
        '.git',
        '.next',
        'dist',
        'build',
      ],
      ...options,
    };

    this.stats = {
      filesScanned: 0,
      filesModified: 0,
      importsFixed: 0,
      errors: [],
    };
  }

  async run() {
    console.log('🔍 Scanning for lodash import issues...\n');

    try {
      const files = await this.findFiles();

      for (const file of files) {
        await this.processFile(file);
      }

      this.printSummary();
    } catch (error) {
      console.error('❌ Error running script:', error.message);
      process.exit(1);
    }
  }

  async findFiles() {
    const pattern = `**/*.{${this.options.extensions.join(',')}}`;
    const excludePattern = `{${this.options.excludeDirs.join(',')}}/**`;

    const files = await glob(pattern, {
      cwd: this.options.rootDir,
      ignore: excludePattern,
      absolute: true,
    });

    if (this.options.verbose) {
      console.log(`Found ${files.length} files to scan`);
    }

    return files;
  }

  async processFile(filePath) {
    try {
      const content = await fs.promises.readFile(filePath, 'utf8');
      const result = this.fixLodashImports(content, filePath);

      this.stats.filesScanned++;

      if (result.modified) {
        this.stats.filesModified++;
        this.stats.importsFixed += result.fixedCount;

        if (!this.options.dryRun) {
          await fs.promises.writeFile(filePath, result.content);
        }

        console.log(
          `✅ ${path.relative(this.options.rootDir, filePath)} (${result.fixedCount} fixes)`
        );

        if (this.options.verbose) {
          result.changes.forEach((change) => {
            console.log(
              `   ${change.line}: ${change.original} → ${change.fixed}`
            );
          });
        }
      } else if (this.options.verbose) {
        console.log(
          `⚪ ${path.relative(this.options.rootDir, filePath)} (no changes needed)`
        );
      }
    } catch (error) {
      this.stats.errors.push({ file: filePath, error: error.message });
      console.error(`❌ Error processing ${filePath}: ${error.message}`);
    }
  }

  fixLodashImports(content, filePath) {
    const lines = content.split('\n');
    let modified = false;
    let fixedCount = 0;
    const changes = [];

    const patterns = [
      {
        regex: /^(\s*)import\s+\*\s+as\s+_\s+from\s+['"]lodash['"](.*)$/,
        type: 'namespace',
      },
      {
        regex: /^(\s*)import\s+_\s+from\s+['"]lodash['"](.*)$/,
        type: 'default',
      },
      {
        regex: /^(\s*)import\s+\{\s*([^}]+)\s*\}\s+from\s+['"]lodash['"](.*)$/,
        type: 'destructured',
      },
    ];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      let lineModified = false;

      for (const pattern of patterns) {
        const match = line.match(pattern.regex);
        if (match) {
          const newImports = this.generateOptimizedImports(
            match,
            pattern.type,
            filePath,
            i + 1
          );
          if (newImports && newImports.length > 0) {
            lines[i] = newImports.join('\n');
            modified = true;
            lineModified = true;
            fixedCount++;

            changes.push({
              line: i + 1,
              original: line.trim(),
              fixed: newImports.join('; '),
            });
            break;
          }
        }
      }

      if (!lineModified) {
        const modifiedLine = this.replaceUsagePatterns(line);
        if (modifiedLine !== line) {
          lines[i] = modifiedLine;
          modified = true;
        }
      }
    }

    return {
      content: lines.join('\n'),
      modified,
      fixedCount,
      changes,
    };
  }

  generateOptimizedImports(match, type, filePath, lineNumber) {
    const [, indent, , semicolon = ''] = match;

    switch (type) {
      case 'namespace':
      case 'default':
        return [
          `${indent}// TODO: Replace with specific lodash imports like: import method from 'lodash/method'`,
          `${indent}// Original import: ${match[0].trim()}`,
        ];

      case 'destructured':
        const methods = match[2]
          .split(',')
          .map((m) => m.trim())
          .filter((m) => m.length > 0)
          .map((m) => {
            const [methodName, alias] = m.includes(' as ')
              ? m.split(' as ')
              : [m, null];
            return { name: methodName.trim(), alias: alias?.trim() };
          });

        return methods.map(({ name, alias }) => {
          const importName = alias || name;
          return `${indent}import ${importName} from 'lodash/${name}'${semicolon}`;
        });
    }

    return null;
  }

  replaceUsagePatterns(line) {
    const commonMethods = [
      'map',
      'filter',
      'reduce',
      'find',
      'forEach',
      'includes',
      'isEmpty',
      'isArray',
      'isObject',
      'isString',
      'isNumber',
      'clone',
      'cloneDeep',
      'merge',
      'pick',
      'omit',
      'get',
      'set',
      'has',
      'keys',
      'values',
      'flatten',
      'uniq',
      'sortBy',
      'groupBy',
      'debounce',
      'throttle',
    ];

    let modifiedLine = line;

    commonMethods.forEach((method) => {
      const regex = new RegExp(`\\b_\\.${method}\\b`, 'g');
      if (regex.test(line)) {
        if (!line.includes('// TODO:')) {
          modifiedLine =
            line +
            ` // TODO: Consider importing ${method} from 'lodash/${method}'`;
        }
      }
    });

    return modifiedLine;
  }

  printSummary() {
    console.log('\n📊 Summary:');
    console.log(`Files scanned: ${this.stats.filesScanned}`);
    console.log(`Files modified: ${this.stats.filesModified}`);
    console.log(`Imports fixed: ${this.stats.importsFixed}`);

    if (this.stats.errors.length > 0) {
      console.log(`\n❌ Errors: ${this.stats.errors.length}`);
      this.stats.errors.forEach((error) => {
        console.log(`  ${error.file}: ${error.error}`);
      });
    }

    if (this.options.dryRun) {
      console.log('\n🔍 Dry run mode - no files were actually modified');
      console.log('Run without --dry-run to apply changes');
    } else if (this.stats.filesModified > 0) {
      console.log('\n✅ Import optimization complete!');
      console.log(
        '📝 Note: Some imports may need manual review (check TODO comments)'
      );
    } else {
      console.log('\n✨ No lodash import issues found!');
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: args.includes('--dry-run'),
    verbose: args.includes('--verbose') || args.includes('-v'),
    rootDir: process.cwd(),
  };

  const rootDirIndex = args.findIndex((arg) => arg === '--root-dir');
  if (rootDirIndex !== -1 && args[rootDirIndex + 1]) {
    options.rootDir = path.resolve(args[rootDirIndex + 1]);
  }

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Lodash Import Fixer
Automatically fixes lodash imports for better tree-shaking in Next.js projects.

Usage:
  node fix-lodash-imports.js [options]

Options:
  --dry-run        Show what would be changed without modifying files
  --verbose, -v    Show detailed output
  --root-dir <dir> Specify root directory (default: current directory)
  --help, -h       Show this help message

Examples:
  node fix-lodash-imports.js --dry-run
  node fix-lodash-imports.js --verbose
  node fix-lodash-imports.js --root-dir ./src
`);
    process.exit(0);
  }

  const fixer = new LodashImportFixer(options);
  await fixer.run();
}

if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

module.exports = LodashImportFixer;