const errors = require('./errors');
const convertObjectToCss = require('./convertObjectToCss');
const logErrors = require('./logErrors');

function getId() {
  let result = '';
  for (let i = 0; i < 10; i++) {
    result += Math.floor(Math.random() * 10); // Generates a random digit from 0 to 9
  }
  return result;
}

function convertToScssValidFiles(file) {
  // Defining variables in this context
  const breakpoints = {
    smallMobile: `@media (min-width: breakpoints(smallMobile))`,
    tablet: `@media (min-width: breakpoints(tablet))`,
    midDesktop: `@media (min-width: breakpoints(midDesktop))`,
    desktop: `@media (min-width: breakpoints(desktop))`,
  };
  //
  const pattern = /\(\s*([^()]+)\s*:\s*{?[^}]+}?\s*\)\s*=>\s*/g;
  let modifiedString = file.replace(pattern, '');

  let matchedWords = [];

  const colors = new Proxy(
    {},
    {
      get: function (target, prop) {
        return `colors(${prop})`;
      },
    }
  );

  const variables = new Proxy(
    {},
    {
      get: function (target, prop) {
        return `variables(${prop})`;
      },
    }
  );
  const zIndex = new Proxy(
    {},
    {
      get: function (target, prop) {
        return `zIndex(${prop})`;
      },
    }
  );
  //

  // Use replace method with a callback function to extract words and add them to the array
  file.replace(pattern, (match, words) => {
    const wordList = words
      .split(',')
      .map((word) => word.split(':'))
      ?.flat()
      .map((word) => word.split('}'))
      ?.flat()
      .map((word) => word.split('{'))
      ?.flat()
      .map((word) => word.split(';'))
      ?.flat()
      .map((word) => word.split('\n'))
      ?.flat()
      .map((word) => word.trim())
      ?.filter(Boolean);

    matchedWords.push(...wordList);
  });

  matchedWords = [...new Set(matchedWords)];
  matchedWords.forEach((variableName) => {
    this[variableName] = variableName;
  });

  // replace the string values with ids and store them in an object
  const store = {};
  const contingencyStore = {};
  matchedWords.forEach((variableName) => {
    const pattern = new RegExp(`\\b${variableName}\\b(.*?),`, 'g');
    text = modifiedString.match(pattern)?.join('');

    const id = getId();

    if (text) {
      store[id] = text;
    } else {
      contingencyStore[id] = variableName;
    }

    modifiedString = modifiedString.replace(pattern, `${id},`);
  });
  //

  const storeVariables = {};
  const variablesMatch = modifiedString.match(/:.*variables.*,/g) || [];
  Array.from(variablesMatch)?.forEach((match) => {
    const id = getId();
    storeVariables[id] = match;
    modifiedString = modifiedString.replace(match, `: ${id},`);
  });

  let scss = '';
  function evalWithContext() {
    return eval(`(${modifiedString})`);
  }
  try {
    let obj = evalWithContext.call(this);

    function deepCloneAndInvoke(obj) {
      // Check if the object is a function, and if so, call it to modify the object
      if (typeof obj === 'function') {
        return deepCloneAndInvoke(obj.call(this));
      }
      // If the object is not an object or is null, return it as is
      if (typeof obj !== 'object' || obj === null) {
        return obj;
      }
      // Create a new object to store the modified properties
      const result = Array.isArray(obj) ? [] : {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          // Recursively clone and invoke functions for nested properties
          result[key] = deepCloneAndInvoke(obj[key]);
        }
      }
      return result;
    }
    obj = deepCloneAndInvoke(obj);

    scss = convertObjectToCss(obj);
  } catch (err) {
    console.log(logErrors[4]);
    scss = `${errors.arrowFunction};\n${modifiedString}`;
  }

  Object.keys(storeVariables)?.map((variableKey) => {
    const value = storeVariables[variableKey]?.trim();

    let newValue = value?.slice(1, value?.length - 1);
    const matched = value?.match(/variables\.[^\s]+/)?.[0];
    const splitted = matched?.split('.');
    newValue = newValue.replace(matched, `${splitted[0]}(${splitted[1]})`);
    scss = scss.replace(`${variableKey}px`, newValue);
  });

  Object.keys(store)?.map((variableKey) => {
    scss = scss.replace(`${variableKey}px`, variableKey);
  });
  const errorsString = [
    ...Object.keys(store)?.map((key) => {
      return !!store[key]
        ? `${errors.variableInsideClasses
            ?.concat(key)
            .concat(', ')
            .concat(store[key])};\n`
        : '';
    }),
    ...Object.keys(contingencyStore)?.map((key) => {
      return !!contingencyStore[key]
        ? `${errors.findTheLineYourself
            ?.concat(key)
            .concat(', ')
            .concat(contingencyStore[key])};\n`
        : '';
    }),
  ]
    ?.filter(Boolean)
    .join('');

  let withFlag = !!matchedWords?.length ? `${errorsString};\n${scss}` : scss;

  // add theme import
  withFlag = `@import '/src/shared/theme/theme.scss';\n\n${scss}`;

  return withFlag;
}

module.exports = convertToScssValidFiles;
