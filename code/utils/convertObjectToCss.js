function convertObjectToCss(obj, isPseudo = false) {
  let cssString = '';

  for (const className in obj) {
    if (['@media']?.some((item) => className?.includes(item))) {
      const value = convertObjectToCss(obj[className]);
      cssString += ` ${className} {${value}};`;
      continue;
    }

    if (Object.prototype.hasOwnProperty.call(obj, className)) {
      const cssProperties = obj[className];

      cssString += (isPseudo ? '' : '.') + className + ' {';

      function handleProperty(iProp) {
        if (Object.prototype.hasOwnProperty.call(cssProperties, iProp)) {
          // Convert camelCase to kebab-case for CSS property names
          const cssPropertyName = iProp
            .replace(/([a-z])([A-Z])/g, '$1-$2')
            .toLowerCase();
          // Assign the original CSS value as it is
          let cssValue = cssProperties[iProp];

          // Add "px" to numeric CSS values
          if (typeof cssValue === 'number') {
            cssValue = cssValue + 'px';
          }
          if (Array.isArray(cssValue) && Array.isArray(cssValue?.[0])) {
            cssValue = cssValue?.[0]
              ?.map((item) => {
                if (typeof item === 'number') {
                  return item + 'px';
                }
                return item;
              })
              ?.join(' ');
          }

          cssString += ` ${cssPropertyName}: ${cssValue};`;
        }
      }
      function loopThroughProperties(properties) {
        for (const prop in properties) {
          if (['&', '>', '*']?.some((item) => prop?.includes(item))) {
            const stringed = convertObjectToCss(
              {
                [prop]: properties[prop],
              },
              true
            );

            cssString += stringed;
          } else {
            handleProperty(prop);
          }
        }
      }

      loopThroughProperties(cssProperties);

      cssString += ' }';
    }
  }
  return cssString;
}

module.exports = convertObjectToCss;
