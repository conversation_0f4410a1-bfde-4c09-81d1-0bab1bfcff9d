const fs = require('fs');
const path = require('path');

const directoryPath = 'path/to/your/project'; // Set your project directory path here

function deleteOldFiles(directory) {
  fs.readdir(directory, { withFileTypes: true }, (err, files) => {
    if (err) {
      console.error(`Error reading directory ${directory}:`, err);
      return;
    }

    files.forEach((file) => {
      const fullPath = path.join(directory, file.name);
      if (file.isDirectory()) {
        deleteOldFiles(fullPath);
      } else if (
        file.isFile() &&
        (file.name.endsWith('.old.ts') || file.name.endsWith('.old.tsx'))
      ) {
        fs.unlink(fullPath, (err) => {
          if (err) {
            console.error(`Error deleting file ${fullPath}:`, err);
          } else {
            console.log(`Deleted file: ${fullPath}`);
          }
        });
      }
    });
  });
}

deleteOldFiles(directoryPath);
